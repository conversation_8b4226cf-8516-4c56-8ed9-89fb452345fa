// Imports
import { Injectable } from '@nestjs/common';
import { Op, Sequelize } from 'sequelize';
import {
  PAGE_LIMIT,
  SYSTEM_ADMIN_ID,
  GLOBAL_RANGES,
  GLOBAL_FLOW,
  CSE_ROLE_ID,
  BAD_CIBIL_SCORE_REASON_ID,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  k422ErrorMessage,
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
  kSuccessMessage,
  kWrongDetails,
} from 'src/constants/responses';
import { admin } from 'src/entities/admin.entity';
import { BankingEntity } from 'src/entities/banking.entity';
import { disbursementEntity } from 'src/entities/disbursement.entity';
import { employmentDetails } from 'src/entities/employment.entity';
import { esignEntity } from 'src/entities/esign.entity';
import { KYCEntity } from 'src/entities/kyc.entity';
import { loanTransaction } from 'src/entities/loan.entity';
import { MasterEntity } from 'src/entities/master.entity';
import { PredictionEntity } from 'src/entities/prediction.entity';
import { SalarySlipEntity } from 'src/entities/salarySlip.entity';
import { SubScriptionEntity } from 'src/entities/subscription.entity';
import { registeredUsers } from 'src/entities/user.entity';
import { UserSelfieEntity } from 'src/entities/user.selfie.entity';
import { WorkMailEntity } from 'src/entities/workMail.entity';
import { EmploymentRepository } from 'src/repositories/employment.repository';
import { MasterRepository } from 'src/repositories/master.repository';
import { UserRepository } from 'src/repositories/user.repository';
import { CryptService } from 'src/utils/crypt.service';
import { TypeService } from 'src/utils/type.service';
import { VerificationPrepare } from './verification.prepare';
import {
  BAD_CIBIL_SCORE_MSG,
  kAssignmentSuccessMessage,
  kErrorMsgs,
  kFailed,
  kInitiated,
  kNoDataFound,
} from 'src/constants/strings';
import { RedisService } from 'src/redis/redis.service';
import { BankingRepository } from 'src/repositories/banking.repository';
import {
  kVerificationAccessStatus,
  ON_HOLD_DOCUMENT_REQUIREMENTS,
  REDIS_KEY,
  UserStage,
  YES_NO_OBJECT,
} from 'src/constants/objects';
import {
  kLoanAcceptInfo,
  kSomthinfWentWrong,
  kSubmitTheDetails,
  kpleaseSubmitYourDetails,
  redirectKBankStatement,
  redirectKEmployment,
  salaryMissingDetails,
  redirectKSelectLoanAmount,
  redirectKWorkMail,
  redirectKSalaryOrOffer,
} from 'src/constants/strings';
import { SalarySlipRepository } from 'src/repositories/salarySlip.repository';
import { WorkMailRepository } from 'src/repositories/workMail.repository';
import { LoanRepository } from 'src/repositories/loan.repository';
import { SharedNotificationService } from 'src/shared/services/notification.service';
import { CommonSharedService } from 'src/shared/common.shared.service';
import { UserLogTrackerRepository } from 'src/repositories/userLogTracker.repository';
import { CibilScoreEntity } from 'src/entities/cibil.score.entity';
import { CibilService } from 'src/shared/cibil.service';
import { EligibilitySharedService } from 'src/shared/eligibility.shared.service';
import { SequelOptions } from 'src/interfaces/include.options';
import { AdminService } from '../admin/admin.service';
import { FileService } from 'src/utils/file.service';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { BankingSharedService } from 'src/shared/banking.service';
import { GoogleService } from 'src/thirdParty/google/google.service';
import { EmploymentHistoryRepository } from 'src/repositories/employmentHistory.repository';
import { ChangeLogsRepository } from 'src/repositories/changeLogs.repository';
import { shiftPlanEntity } from 'src/entities/shiftPlan.entity';
import { AssignmentSharedService } from 'src/shared/assignment.service';
import { GoogleCompanyResultEntity } from 'src/entities/company.entity';
import { ErrorContextService } from 'src/utils/error.context.service';
import { EmploymentSharedService } from 'src/shared/employment.shared.service';
import { UserServiceV4 } from 'src/v4/user/user.service.v4';
import { CompanyRepository } from 'src/repositories/google.company.repository';
import { BankingService } from '../banking/banking.service';
import { CAAssignmentService } from 'src/shared/assignment/caAssignCase.service';
import { DateService } from 'src/utils/date.service';
import { CommonService } from 'src/utils/common.service';
import { AdminRedisSyncService } from '../admin/AdminRedisSync.service';

@Injectable()
export class AdminVerificationService {
  constructor(
    private readonly bankingRepo: BankingRepository,
    private readonly userRepository: UserRepository,
    private readonly cryptService: CryptService,
    private readonly prepareService: VerificationPrepare,
    private readonly typeService: TypeService,
    private readonly masterRepo: MasterRepository,
    private readonly employementRepo: EmploymentRepository,
    private readonly redisService: RedisService,
    private readonly sharedCommonService: CommonSharedService,
    private readonly salarySlipRepo: SalarySlipRepository,
    private readonly workMailRepo: WorkMailRepository,
    private readonly loanRepo: LoanRepository,
    private readonly notificationService: SharedNotificationService,
    private readonly userLogTrackerRepo: UserLogTrackerRepository,
    private readonly cibilService: CibilService,
    private readonly sharedEligibility: EligibilitySharedService,
    private readonly adminService: AdminService,
    private readonly fileService: FileService,
    private readonly repoManager: RepositoryManager,
    private readonly sharedbanking: BankingSharedService,
    private readonly googleService: GoogleService,
    private readonly userServiceV4: UserServiceV4,
    private readonly employmentSharedService: EmploymentSharedService,
    private readonly empHistoryRepo: EmploymentHistoryRepository,
    private readonly changeLogsRepo: ChangeLogsRepository,
    private readonly errorContextService: ErrorContextService,
    private readonly assignmentService: AssignmentSharedService,
    private readonly companyRepo: CompanyRepository,
    private readonly bankingService: BankingService,
    private readonly caAssignmentService: CAAssignmentService,
    private readonly dateService: DateService,
    private readonly commonService: CommonService,
    private readonly adminRedisSyncService: AdminRedisSyncService,
  ) {}

  ///for check if active admin is not set in redis
  onModuleInit() {
    this.resetActiveShiftEmployees();
  }

  //#endregion
  commonUserOptions(
    query: any = {},
    userWhere: any = {},
    userInclude: any = [],
    masterWhere: any = {},
    masterAttr: any = [],
    masterInc: any = [],
    veryType: any = [],
    filterFeild = null,
    userAttr: any = [],
    type = null,
  ) {
    try {
      let searchText = query?.searchText;
      const download = query?.download ?? 'false';
      const status = query?.status ?? '0';
      const page = query?.page ?? 1;
      const newOrRepeated = query?.newOrRepeated ?? null;
      const toDay = this.typeService.getGlobalDate(new Date());
      if (searchText) {
        const firstTwoLetters = searchText.substring(0, 2).toLowerCase();
        const restOfString: any = searchText.substring(2);
        if (firstTwoLetters == 'l-' || firstTwoLetters == 'L-')
          masterWhere.loanId = +restOfString;
        else {
          if (!isNaN(searchText)) {
            searchText = this.cryptService.encryptPhone(searchText);
            searchText = searchText.split('===')[1];
            userWhere.phone = { [Op.iRegexp]: searchText };
          } else
            userWhere[Op.or] = [
              { fullName: { [Op.iRegexp]: searchText } },
              { email: { [Op.iRegexp]: searchText } },
            ];
        }
      }
      if (newOrRepeated == '1') userWhere.completedLoans = 0;
      else if (newOrRepeated == '0') userWhere.completedLoans = { [Op.gt]: 0 };
      const masterInclude: any = {
        model: MasterEntity,
        attributes: ['id', 'status', 'rejection', 'loanId', ...masterAttr],
        where: masterWhere,
        include: masterInc,
      };
      if (status == 0) {
        userWhere['NextDateForApply'] = {
          [Op.or]: [{ [Op.lte]: toDay.toJSON() }, { [Op.eq]: null }],
        };
        userWhere['isBlacklist'] = { [Op.ne]: '1' };
        masterWhere.status.loan = { [Op.ne]: 2 };
      }
      if (status != '0' && query.startDate && query.endDate && filterFeild) {
        const range: any = this.typeService.getUTCDateRange(
          query.startDate,
          query.endDate,
        );
        range.fromDate = new Date(range.fromDate).getTime();
        range.endDate = new Date(range.endDate).getTime();
        masterWhere.dates = {
          [filterFeild]: { [Op.gte]: range.fromDate, [Op.lte]: range.endDate },
        };
      }
      let mainOptions: any = {};
      if (query.status == '0') {
        mainOptions = {
          where: {
            ...userWhere,
          },
          include: [...userInclude, masterInclude],
        };
      } else {
        const userMainInclude: any = {
          model: registeredUsers,
          attributes: [
            'id',
            'fullName',
            'phone',
            'createdAt',
            'completedLoans',
            ...userAttr,
          ],
          where: {
            ...userWhere,
          },
          required: true,
          include: [...userInclude],
        };
        const masterInclude = {
          required: true,
          where: masterWhere,
          model: MasterEntity,
          attributes: [...masterAttr, 'status', 'rejection', 'loanId', 'dates'],
          include: [...masterInc],
        };
        if (type == 'EMP') {
          mainOptions = {
            include: [userMainInclude, masterInclude],
            order: [['verifiedDate', 'DESC']],
          };
        } else {
          mainOptions = {
            where: masterWhere,
            include: [...masterInc, userMainInclude],
            order: [[`dates.${filterFeild}::timestamp`, 'ASC']],
          };
        }
      }
      if (status != '0' && download != 'true') {
        mainOptions.offset = +(page ?? 1) * PAGE_LIMIT - PAGE_LIMIT;
        mainOptions.limit = PAGE_LIMIT;
      }
      return mainOptions;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getEmployementVerificationData(query) {
    try {
      const adminid = query.adminid;
      const empOption = await this.prepareEmployementOptions(query);
      if (empOption.message) return kInternalError;
      let data: any = [];

      const attr = [
        'id',
        'userId',
        'companyName',
        'companyPhone',
        'companyUrl',
        'createdAt',
        'companyAddress',
        'companyStatusApproveByName',
        'companyVerification',
        'updatedAt',
        'verifiedDate',
        'rejectReason',
      ];
      data = await this.employementRepo.getTableWhereDataWithCounts(
        attr,
        empOption,
      );
      if (data == k500Error) return kInternalError;
      data.rows = await this.prepareService.prepareCompanyData(
        data.rows,
        adminid,
      );
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  prepareEmployementOptions(query) {
    try {
      const status = query?.status ?? '0';
      // Salary slip table join
      const salarySlipAttributes = [
        'approveById',
        'url',
        'status',
        'salaryVerifiedDate',
        'createdAt',
        'updatedAt',
        'netPayAmount',
        'rejectReason',
        'approveById',
      ];
      const salaryInclude = {
        model: SalarySlipEntity,
        attributes: salarySlipAttributes,
      };
      // Work mail table join
      const workMailInclude = {
        model: WorkMailEntity,
        attributes: ['approveById', 'id', 'status', 'email', 'rejectReason'],
      };
      let userWhere: any = {};
      let masterWhere: any = {};
      if (status == '2')
        masterWhere.status = {
          [Op.and]: {
            bank: { [Op.ne]: '-1' },
            [Op.or]: [{ company: 2 }, { workMail: 2 }, { salarySlip: 2 }],
          },
        };
      else if (status == '1') {
        const approvedStatus = [1, 3, 4];
        masterWhere.status = {
          [Op.and]: {
            bank: { [Op.ne]: '-1' },
            company: { [Op.or]: approvedStatus },
            workMail: { [Op.or]: approvedStatus },
            salarySlip: { [Op.or]: approvedStatus },
          },
        };
      } else {
        const allStatus = [1, 2, 3, 0, 4];
        masterWhere.status = {
          [Op.and]: {
            bank: { [Op.ne]: '-1' },
            company: { [Op.or]: allStatus },
            workMail: { [Op.or]: allStatus },
            salarySlip: { [Op.or]: allStatus },
          },
        };
      }
      const attr = [
        'id',
        'userId',
        'companyName',
        'companyPhone',
        'companyUrl',
        'companyAddress',
        'companyStatusApproveByName',
        'companyVerification',
        'updatedAt',
        'rejectReason',
      ];
      const empInclude: any = {
        model: employmentDetails,
        attributes: attr,
        required: true,
      };
      const masterInclude = [empInclude, salaryInclude, workMailInclude];
      const userOptions = this.commonUserOptions(
        query,
        userWhere,
        [],
        masterWhere,
        ['rejection', 'salarySlipId', 'workMailId', 'loanId', 'empId'],
        masterInclude,
        ['EMPLOYMENT'],
        'employment',
        [],
        'EMP',
      );
      return userOptions;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async prepareBankOptions(query) {
    try {
      const status = query?.status ?? '0';
      const adminId = query?.adminId;

      const userWhere: any = {};
      const masterWhere: any = {};
      const previousCon = {
        company: { [Op.in]: [1, 3] },
        workMail: { [Op.in]: [1, 3, 4] },
        salarySlip: { [Op.in]: [1, 3] },
      };
      if (status == '0')
        masterWhere.status = {
          ...previousCon,
          bank: +status,
        };
      else if (status == '1')
        masterWhere.status = {
          bank: { [Op.or]: [1, 3] },
        };
      else if (status == '2')
        masterWhere.status = { bank: 2, loan: { [Op.ne]: 2 } };
      else if (status == '5') masterWhere.status = { loan: 2, bank: 2 };
      else
        masterWhere.status = {
          ...previousCon,
          bank: { [Op.or]: [1, 2, 3, 0] },
        };

      if (adminId) masterWhere.bankAssingId = adminId;
      const bankingInclude = {
        model: BankingEntity,
        attributes: [
          'id',
          'userId',
          'name',
          'accountNumber',
          'bankStatement',
          'stmtStartDate',
          'stmtEndDate',
          'bank',
          'adminId',
          'salary',
          'salaryDate',
          'rejectReason',
          'status',
          'salaryVerification',
          'attempts',
          'additionalBankStatement',
          'addStmtStartDate',
          'addStmtEndDate',
          'isNeedAdditional',
          'additionalURLs',
          'additionalURLsDates',
          'createdAt',
          'updatedAt',
          'isNeedTagSalary',
          'assignedTo',
          'otherDetails',
        ],

        required: true,
      };
      const attr = [
        'id',
        'userId',
        'companyName',
        'companyPhone',
        'companyUrl',
        'companyAddress',
        'salary',
        'salaryDate',
        'updatedAt',
      ];
      const empInclude: any = {
        model: employmentDetails,
        attributes: attr,
        required: true,
      };
      const loanData = {
        attributes: ['id', 'loanStatus', 'createdAt'],
        model: loanTransaction,
        include: [bankingInclude],
        required: true,
      };
      const cibilInclude = {
        model: CibilScoreEntity,
        attributes: ['id', 'userId', 'cibilScore', 'plScore'],
        required: false,
      };
      const masterInclude = [loanData, empInclude];
      const userOptions = this.commonUserOptions(
        query,
        userWhere,
        [cibilInclude],
        masterWhere,
        ['bankAssingId'],
        masterInclude,
        ['NETBANKING'],
        'banking',
      );
      return userOptions;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funGetBankStatementVerificationData(query) {
    try {
      const bankOptions = await this.prepareBankOptions(query);
      query.status = query?.status ?? '0';
      if (bankOptions.message) return kInternalError;
      const attributes = ['id', 'fullName', 'city', 'phone', 'completedLoans'];
      let data: any = [];
      if (query.status == '0')
        data = await this.userRepository.getTableWhereDataWithCounts(
          attributes,
          bankOptions,
        );
      else {
        const masterAttributes = ['id', 'loanId', 'dates', 'bankAssingId'];
        data = await this.masterRepo.getTableWhereDataWithCounts(
          masterAttributes,
          bankOptions,
        );
      }
      if (data == k500Error) return kInternalError;
      data.rows = await this.prepareService.prepareBankingRowData(
        data.rows,
        query,
      );
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async preapareSelfieOptions(query) {
    try {
      const status = query?.status ?? '0';
      const kycInclude = {
        model: KYCEntity,
        attributes: ['id', 'aadhaarFront', 'profileImage'],
      };
      const selfieInclude: any = {
        model: UserSelfieEntity,
        attributes: [
          'id',
          'image',
          'status',
          'tempImage',
          'adminId',
          'response',
          'rejectReason',
          'updatedAt',
        ],
      };
      const approvedStatus = [1, 3];
      const empApproved = [1, 3, 4];
      const masterWhere: any = {};
      const previouspproved = {
        company: { [Op.or]: empApproved },
        workMail: { [Op.or]: empApproved },
        salarySlip: { [Op.or]: empApproved },
        bank: { [Op.or]: approvedStatus },
      };
      if (status == '0') {
        masterWhere.status = {
          ...previouspproved,
          selfie: 0,
        };
      } else if (status == '1')
        masterWhere.status = {
          selfie: { [Op.or]: approvedStatus },
        };
      else if (status == '2') masterWhere.status = { selfie: +status };
      else
        masterWhere.status = {
          selfie: { [Op.or]: [1, 2, 3, 0] },
        };
      const userInclude = [selfieInclude, kycInclude];
      const userAttr = ['selfieId', 'kycId'];
      const userOptions = this.commonUserOptions(
        query,
        {},
        userInclude,
        masterWhere,
        ['dates'],
        [],
        ['SELFIE'],
        'selfie',
        userAttr,
      );
      return userOptions;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funSelfieVerification(query) {
    try {
      const adminid = query?.adminid;
      const maskOptions = await this.sharedCommonService.findMaskRole(adminid);
      const userOptions: any = await this.preapareSelfieOptions(query);
      if (userOptions.message) return userOptions;
      const attributes = ['id', 'fullName', 'phone', 'city', 'completedLoans'];
      let data: any = [];
      if (query.status == '0')
        data = await this.userRepository.getTableWhereDataWithCounts(
          attributes,
          userOptions,
        );
      else {
        const masterAttributes = [
          'id',
          'coolOffData',
          'status',
          'rejection',
          'dates',
          'loanId',
          'empId',
          'userId',
        ];
        data = await this.masterRepo.getTableWhereDataWithCounts(
          masterAttributes,
          userOptions,
        );
      }
      if (data == k500Error) return kInternalError;
      const finalData: any = this.prepareService.prepareSelfieVerificationData(
        data.rows,
        maskOptions,
      );
      if (finalData.message) return finalData;
      data.rows = finalData;
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  prepareKycOptions(query) {
    try {
      const status = query.status;
      let masterWhere: any = {};
      const approvedStatus = [1, 3];
      const empStatus = [1, 3, 4];

      const kycAttr = [
        'id',
        'userId',
        'maskedAadhaar',
        'aadhaarStatus',
        'aadhaarFront',
        'aadhaarBack',
        'aadhaarVerifiedAdmin',
        'aadhaarRejectReason',
        'aadhaarResponse',
        'pan',
        'panCardNumber',
        'panStatus',
        'panResponse',
        'panVerifiedAdmin',
        'panUploadedAdmin',
        'panRejectReason',
        'otherDocType',
        'otherDocFront',
        'otherDocBack',
        'otherDocStatus',
        'otherDocResponse',
        'otherDocRejectReason',
        'otherDocVerifiedAdmin',
        'otherDocUploadedAdmin',
      ];
      let kycInclude = {
        model: KYCEntity,
        attributes: kycAttr,
      };
      const userInclude = [kycInclude];
      const previouspproved = {
        company: { [Op.or]: empStatus },
        workMail: { [Op.or]: empStatus },
        salarySlip: { [Op.or]: empStatus },
        bank: { [Op.or]: approvedStatus },
      };
      if (status == 0) {
        masterWhere = {
          status: {
            ...previouspproved,
            pan: 0,
          },
        };
      } else if (status == 1) {
        masterWhere.status = {
          aadhaar: { [Op.or]: approvedStatus },
          pan: { [Op.or]: approvedStatus },
        };
      } else if (status == 2) {
        masterWhere.status = {
          [Op.or]: [{ aadhaar: status }, { pan: status }],
        };
      } else {
        const allStatus = [1, 2, 3, 0];
        masterWhere.status = {
          ...previouspproved,
          [Op.or]: [
            { aadhaar: { [Op.or]: allStatus } },
            { pan: { [Op.or]: allStatus } },
          ],
        };
      }
      const userAttr = ['kycId', 'completedLoans', 'phone', 'city'];
      const userOptions = this.commonUserOptions(
        query,
        {},
        userInclude,
        masterWhere,
        [],
        [],
        ['KYC'],
        'pan',
        userAttr,
      );
      return userOptions;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funKycVerification(query) {
    try {
      const adminid = query?.adminid;
      const userOptions = this.prepareKycOptions(query);
      const attributes = [
        'id',
        'fullName',
        'phone',
        'city',
        'completedLoans',
        'lastOnlineTime',
      ];
      let data: any = [];
      if (query.status == '0')
        data = await this.userRepository.getTableWhereDataWithCounts(
          attributes,
          userOptions,
        );
      else {
        const masterAttributes = [
          'id',
          'coolOffData',
          'status',
          'rejection',
          'dates',
          'loanId',
          'empId',
          'userId',
        ];
        data = await this.masterRepo.getTableWhereDataWithCounts(
          masterAttributes,
          userOptions,
        );
      }
      if (data == k500Error) return kInternalError;
      const finalData: any =
        await this.prepareService.prepareKycVerificationData(
          data.rows,
          adminid,
        );
      if (finalData.message) return finalData;
      data.rows = finalData;
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  prepareFinalVerificationOptions(query) {
    try {
      const status = query?.status ?? 0;
      const adminId = query.adminId;
      const masterWhere: any = {};
      const approvedStatus = [1, 3];
      const empStatus = [1, 3, 4];

      const previouspproved = {
        company: { [Op.or]: empStatus },
        workMail: { [Op.or]: empStatus },
        salarySlip: { [Op.or]: empStatus },
        bank: { [Op.or]: approvedStatus },
      };
      if (status == 0) {
        masterWhere.status = {
          ...previouspproved,
          loan: 0,
          eligibility: 0,
        };
      } else if (status == 1) {
        masterWhere.status = {
          eligibility: { [Op.or]: approvedStatus },
        };
      } else if (status == 2) {
        masterWhere.status = {
          ...previouspproved,
          eligibility: +status,
        };
      } else {
        const allStatus = [0, 1, 3, 2];
        masterWhere.status = {
          ...previouspproved,
          eligibility: { [Op.or]: allStatus },
        };
      }
      if (adminId) masterWhere.loanAssingId = adminId;
      const loanAttr = [
        'id',
        'manualVerification',
        'empId',
        'userId',
        'loanAmount',
        'netApprovedAmount',
        'manualVerificationAcceptName',
        'manualVerificationAcceptId',
        'prediction',
        'updatedAt',
        'bankingId',
        'remark',
        'loanRejectReason',
        'approvedReason',
        'categoryTag',
      ];
      const loanInclude = {
        model: loanTransaction,
        attributes: loanAttr,
        include: [
          { model: PredictionEntity, attributes: ['reason'], required: false },
          {
            model: admin,
            as: 'adminData',
            attributes: ['id', 'fullName'],
            required: false,
          },
          {
            model: BankingEntity,
            attributes: [
              'id',
              'salary',
              'adminSalary',
              'disbursementIFSC',
              'salaryVerification',
              'assignedTo',
            ],
          },
        ],
      };
      const loanAssignInclude = {
        model: admin,
        as: 'loanAssignData',
        attributes: ['id', 'fullName'],
        required: false,
      };
      const userOptions = this.commonUserOptions(
        query,
        {},
        [],
        masterWhere,
        ['loanAssingId'],
        [loanInclude, loanAssignInclude],
        ['FINALBUCKET'],
        'eligibility',
      );
      return userOptions;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  async funFinalVerificationData(query) {
    try {
      const userOptions = this.prepareFinalVerificationOptions(query);
      if (userOptions.message) return userOptions;
      const attributes = [
        'id',
        'fullName',
        'phone',
        'city',
        'completedLoans',
        'lastOnlineTime',
      ];
      let data: any = [];
      if (query.status == '0')
        data = await this.userRepository.getTableWhereDataWithCounts(
          attributes,
          userOptions,
        );
      else {
        const masterAttributes = [
          'id',
          'coolOffData',
          'status',
          'rejection',
          'dates',
          'loanId',
          'empId',
          'userId',
        ];
        data = await this.masterRepo.getTableWhereDataWithCounts(
          masterAttributes,
          userOptions,
        );
      }
      if (data == k500Error) return kInternalError;
      const finalData = await this.prepareService.prepareFinalVerificationData(
        data.rows,
      );
      data.rows = finalData;
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  prepareEmandateOptions(query) {
    try {
      const subscriptionInclude: any = { model: SubScriptionEntity };
      subscriptionInclude.required = false;
      subscriptionInclude.attributes = [
        'id',
        'accountNumber',
        'subType',
        'mode',
        'status',
        'createdAt',
        'updatedAt',
        'invitationLink',
        'response',
      ];

      const bankInclude = {
        model: BankingEntity,
        attributes: ['mandateBank', 'mandateAccount', 'mandateBank'],
      };
      const include = [subscriptionInclude, bankInclude];
      const loanAttr = [
        'id',
        'mandateAttempts',
        'updatedAt',
        'netApprovedAmount',
      ];
      const loanInclude = {
        model: loanTransaction,
        attributes: loanAttr,
        include,
      };
      let pending = [0, -1, 2];
      const masterWhere = {
        status: {
          [Op.and]: [
            { eligibility: { [Op.in]: [1, 3] } },
            { loan: { [Op.in]: [1, 3] } },
            { eMandate: { [Op.in]: pending } },
          ],
        },
      };
      const userOptions = this.commonUserOptions(
        query,
        {},
        [],
        masterWhere,
        [],
        [loanInclude],
        ['MANDATE'],
      );
      return userOptions;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funEmandateVerificationData(query) {
    try {
      query.status = '0';
      const userOptions: any = this.prepareEmandateOptions(query);
      if (userOptions.message) return userOptions;
      const attributes = [
        'id',
        'fullName',
        'phone',
        'city',
        'completedLoans',
        'lastOnlineTime',
        'typeOfDevice',
      ];
      const data: any = await this.userRepository.getTableWhereDataWithCounts(
        attributes,
        userOptions,
      );
      if (data == k500Error) return kInternalError;
      const finalData: any = this.prepareService.prepareMandateData(data.rows);
      if (finalData.message) return finalData;
      data.rows = finalData;
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funEsignVerificationData(query) {
    try {
      query.status = '0';
      const esignInclude = {
        model: esignEntity,
        attributes: [
          'id',
          'quick_invite_url',
          'updatedAt',
          'createdAt',
          'nameMissMatch',
          'name_as_per_aadhaar',
          'response',
          'status',
        ],
      };
      const loanInclude = {
        model: loanTransaction,
        attributes: ['id', 'esign_id', 'netApprovedAmount'],
        include: [esignInclude],
      };
      const approvedStatus = [1, 3];
      const masterWhere = {
        status: {
          eligibility: { [Op.or]: [1, 3] },
          loan: { [Op.or]: [1, 3] },
          eMandate: { [Op.or]: approvedStatus },
          eSign: { [Op.or]: [-1, 0] },
        },
      };
      const userOptions = this.commonUserOptions(
        query,
        {},
        [],
        masterWhere,
        [],
        [loanInclude],
        ['ESIGN'],
      );
      if (userOptions.message) return userOptions;
      const attributes = [
        'id',
        'fullName',
        'city',
        'completedLoans',
        'lastOnlineTime',
      ];
      const data = await this.userRepository.getTableWhereDataWithCounts(
        attributes,
        userOptions,
      );
      if (data == k500Error) return kInternalError;
      const finalData: any = this.prepareService.prepareEsignVerificationData(
        data.rows,
        query.download,
      );
      if (finalData.message) return finalData;
      data.rows = finalData;
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funDisburesementVerificationData(query) {
    try {
      query.status = '0';

      const disbursementInclude: any = {
        distinct: true,
        model: disbursementEntity,
        attributes: ['id', 'status', 'bank_name', 'response'],
      };
      const loanInclude: any = {
        model: loanTransaction,
        attributes: ['id', 'netApprovedAmount'],
        distinct: true,
        include: [disbursementInclude],
      };
      const masterWhere = {
        status: {
          eligibility: { [Op.or]: [1, 3] },
          loan: { [Op.or]: [1, 3] },
          eSign: 1,
          eMandate: 1,
          disbursement: { [Op.or]: [0, -1] },
        },
      };
      const userOptions = this.commonUserOptions(
        query,
        {},
        [],
        masterWhere,
        [],
        [loanInclude],
        ['DISBURSEMENT'],
      );
      if (userOptions.message) return kInternalError;
      const attributes = [
        'id',
        'fullName',
        'city',
        'phone',
        'completedLoans',
        'lastOnlineTime',
      ];
      const data: any = await this.userRepository.getTableWhereDataWithCounts(
        attributes,
        userOptions,
      );
      if (data == k500Error) return kInternalError;
      const finalData = await this.prepareService.prepareDisbursementData(
        data.rows,
      );
      data.rows = finalData;
      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async funGetAllVerificationCount() {
    try {
      const verificationObj = {
        employement: 0,
        banking: 0,
        selfie: 0,
        kyc: 0,
        finalVerification: 0,
        mandate: 0,
        esign: 0,
        disbursement: 0,
      };
      let masterWhere: any = {};
      const approvedStatus = [1, 3];

      masterWhere.status = {
        [Op.and]: {
          loan: { [Op.in]: [-1, -2, 7] },
          bank: { [Op.in]: [0, 1, 3, 4] },
          company: { [Op.ne]: 2 },
          salarySlip: { [Op.ne]: 2 },
          workMail: { [Op.ne]: 2 },
          [Op.or]: [{ company: 0 }, { workMail: 0 }, { salarySlip: 0 }],
        },
      };
      verificationObj.employement = await this.getVerificationCount(
        masterWhere,
      );
      const empApproved = [1, 3, 4];
      const previouspproved: any = {
        bank: { [Op.ne]: -1 },
        company: { [Op.or]: empApproved },
        workMail: { [Op.or]: empApproved },
        salarySlip: { [Op.or]: empApproved },
      };
      masterWhere.status = {
        ...previouspproved,
        bank: 0,
      };
      verificationObj.banking = await this.getVerificationCount(masterWhere);
      previouspproved.bank = { [Op.or]: approvedStatus };
      masterWhere.status = {
        ...previouspproved,
      };
      masterWhere.status = {
        ...previouspproved,
        selfie: 0,
      };
      verificationObj.selfie = await this.getVerificationCount(masterWhere);
      previouspproved.selfie = { [Op.or]: approvedStatus };
      masterWhere.status = {
        ...previouspproved,
      };
      masterWhere = {
        status: {
          ...previouspproved,
          pan: 0,
        },
      };
      verificationObj.kyc = await this.getVerificationCount(masterWhere);
      previouspproved.pan = { [Op.or]: approvedStatus };
      masterWhere.status = {
        ...previouspproved,
        eligibility: 0,
        loan: 0,
      };
      verificationObj.finalVerification = await this.getVerificationCount(
        masterWhere,
      );
      masterWhere = {
        status: {
          [Op.and]: {
            eligibility: { [Op.or]: [1, 3] },
            loan: { [Op.or]: [1, 3] },
            eMandate: { [Op.or]: [0, -1, 2] },
          },
        },
      };
      verificationObj.mandate = await this.getVerificationCount(masterWhere);
      masterWhere.status = {
        eligibility: { [Op.or]: [1, 3] },
        loan: { [Op.or]: [1, 3] },
        eMandate: 1,
        eSign: { [Op.or]: [-1, 0] },
      };
      verificationObj.esign = await this.getVerificationCount(masterWhere);
      masterWhere.status = {
        eligibility: { [Op.or]: [1, 3] },
        loan: { [Op.or]: [1, 3] },
        eMandate: 1,
        eSign: 1,
        disbursement: { [Op.or]: [-1, 0] },
      };
      verificationObj.disbursement = await this.getVerificationCount(
        masterWhere,
      );
      return verificationObj;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async getVerificationCount(masterWhere: any = {}) {
    try {
      const toDay = this.typeService.getGlobalDate(new Date());
      const userOptions: any = {
        where: {
          isBlacklist: { [Op.ne]: '1' },
          NextDateForApply: {
            [Op.or]: [{ [Op.lte]: toDay.toJSON() }, { [Op.eq]: null }],
          },
        },
      };
      masterWhere.status.loan = { [Op.ne]: 2 };
      const masterInclude = { model: MasterEntity, where: masterWhere };
      userOptions.include = [masterInclude];
      const data = await this.userRepository.getCountsWhere(userOptions);
      if (data === k500Error) return 0;
      return data;
    } catch (error) {
      return 0;
    }
  }

  async updateDates() {
    try {
      const data = await this.masterRepo.getTableWhereData(['id', 'dates'], {});
      if (data == k500Error) return kInternalError;
      for (let i = 0; i < data.length; i++) {
        try {
          const each = data[i];
          await this.masterRepo.updateRowData({ dates: each.dates }, each.id);
        } catch (error) {}
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region get redirect row Data
  private async getRedirectRowData(loanId, type: string = '') {
    try {
      const loanInclude = {
        model: loanTransaction,
        attributes: ['bankingId'],
        where: {
          loanStatus: ['InProcess', 'Accepted'],
          id: loanId,
        },
      };
      const options: any = {
        where: { status: { loan: { [Op.or]: [-2, -1, 0, 4, 5] } }, loanId },
        include: [loanInclude],
      };
      const att = ['id', 'status'];
      if (type) {
        options.include.push({
          model: registeredUsers,
          attributes: ['id', 'fcmToken'],
        });
        att.push(
          ...[
            'dates',
            'rejection',
            'userId',
            'empId',
            'workMailId',
            'salarySlipId',
          ],
        );
      }
      const masterData = await this.masterRepo.getRowWhereData(att, options);
      if (masterData == k500Error) return kInternalError;
      return masterData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region get redirect list
  private getRedirectList(status) {
    const statusList = [0, 1, 3, 4, 5, 6];
    const finalList = [];
    /// company
    if (statusList.includes(status?.company))
      finalList.push(redirectKEmployment);

    /// salarySlip
    if (statusList.includes(status?.salarySlip)) {
      finalList.push(redirectKSalaryOrOffer);
    }

    /// workMail
    if (statusList.includes(status?.workMail))
      finalList.push(redirectKWorkMail);

    /// bank
    if (statusList.includes(status?.bank))
      finalList.push(redirectKBankStatement);

    return finalList;
  }
  //#endregion

  // User tracking logs
  async getUserTrackingLogs(reqData) {
    try {
      // Params validation
      const loanId = reqData?.loanId;
      const isDownload = (reqData?.download ?? 'false') == 'true';
      const userId = reqData.userId;
      if (!userId) return kParamMissing('userId');

      // Table joins
      const disbursementInclude: any = { model: disbursementEntity };
      disbursementInclude.attributes = ['createdAt'];
      disbursementInclude.required = false;
      const include = [disbursementInclude];

      const attributes = ['id', 'loanStatus', 'updatedAt'];
      const options = { include, order: [['id', 'ASC']], where: { userId } };
      const loanList = await this.loanRepo.getTableWhereData(
        attributes,
        options,
      );
      if (loanList == k500Error) return kInternalError;
      let firstLoan;
      if (loanId) {
        const index = loanList.findIndex((el) => el.id == loanId);
        firstLoan = index == 0;
        const loanData = loanList[index];
        let maxDate;
        if (
          loanData.loanStatus == 'Active' ||
          loanData.loanStatus == 'Complete'
        ) {
          const disbursementList = loanData.disbursementData ?? [];
          if (disbursementList.length > 0)
            maxDate = disbursementList[0].createdAt;
        } else if (loanData.loanStatus == 'Rejected')
          maxDate = loanData.updatedAt;
      }
      const logAttr = [
        'id',
        'userId',
        'loanId',
        'stage',
        'createdAt',
        'ip',
        'deviceId',
        'city',
        'ipLocation',
        'ipCountry',
        'otherDetails',
      ];
      const where: any = {
        userId,
        stage: { [Op.ne]: 'Payment Order Created' },
      };
      if (loanId) where.loanId = loanId;
      if (firstLoan) where.loanId = { [Op.or]: [loanId, null] };

      const LogOptions = { order: [['id', 'ASC']], where };
      const logList = await this.userLogTrackerRepo.getTableWhereData(
        logAttr,
        LogOptions,
      );
      if (logList == k500Error) return kInternalError;
      const data = await this.prepareData(logList);

      if (isDownload) {
        const rawExcelData = {
          sheets: ['User Logs'],
          data: [data],
          sheetName: 'User Logs.xlsx',
          needFindTuneKey: true,
        };
        const url: any = await this.fileService.objectToExcelURL(rawExcelData);
        if (url?.message) return url;
        return { fileUrl: url };
      }

      return data;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region Update Redirect to specific step
  async redirectToSpecificStep(body): Promise<any> {
    try {
      const loanId = body?.loanId;
      const type = body?.type;
      if (!body?.adminId || !loanId || !type) return kParamMissing();
      /// find Data
      const masterData = await this.getRedirectRowData(loanId, type);
      if (masterData?.message) return masterData;
      if (!masterData) return k422ErrorMessage(kSomthinfWentWrong);
      /// prepare list and check type is exite
      const finalList = this.getRedirectList(masterData?.status);
      const find = finalList.find((f) => f.key === type);
      if (!find) return k422ErrorMessage(kSomthinfWentWrong);
      /// reject steps
      await this.rejectRedirectStep(masterData, body, loanId);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region reject redirect step
  private async rejectRedirectStep(masterData, body, loanId) {
    try {
      const type = body?.type;
      const adminId = body?.adminId;
      let content = body?.content ?? '';
      let title = body?.title ?? '';
      const status = masterData?.status;
      const dates = masterData?.dates;
      const rejection = masterData?.rejection;
      const updateData: any = { status, dates, rejection };
      const statusList = [0, 1, 3, 4, 5, 6];
      let selectLoanAmount = false;
      let selectCompany = false;
      let sendNotification = false;

      //#region Company
      if (type == redirectKEmployment.key) {
        if (statusList.includes(status.salarySlip)) {
          await this.rejectCompany(masterData, updateData, content, adminId);
          selectCompany = true;
          sendNotification = true;
        }
      }
      //#endregion

      //#region salary slip
      if (type == redirectKSalaryOrOffer.key || selectCompany) {
        if (statusList.includes(status.salarySlip)) {
          await this.rejectSalarySlip(masterData, updateData, content, adminId);
          sendNotification = true;
        }
      }
      //#endregion

      //#region work mail
      if (type == redirectKWorkMail.key || selectCompany) {
        if (statusList.includes(status.workMail)) {
          await this.rejectWorkMail(masterData, updateData, content, adminId);
          sendNotification = true;
        }
      }
      //#endregion

      //#region bank
      if (type == redirectKBankStatement.key || selectCompany) {
        if (statusList.includes(status.bank)) {
          await this.rejectBank(masterData, updateData, content, adminId);
          selectLoanAmount = true;
          sendNotification = true;
        }
      }
      //#endregion

      //#region Select loan amount routs
      if (type == redirectKSelectLoanAmount.key || selectLoanAmount) {
        const loan = status.loan;
        if (loan == 0 || loan == 4 || loan == 5) {
          await this.selectLoanAmount(updateData, loanId);
          sendNotification = true;
          if (!selectLoanAmount) {
            title = kLoanAcceptInfo.title;
            content =
              'You are eligible for the loan. Accept the loan to proceed further';
          }
        }
      }
      //#endregion

      /// update master
      const id = masterData.id;
      const result = await this.masterRepo.updateRowData(updateData, id);
      if (result == k500Error) return kInternalError;

      const fcmToken = masterData?.userData?.fcmToken;
      if (!content) {
        title = kSubmitTheDetails;
        content = kpleaseSubmitYourDetails;
      }
      if (fcmToken && content && sendNotification) {
        await this.notificationService.sendPushNotification(
          fcmToken,
          title ?? kSubmitTheDetails,
          content,
          {},
          true,
          adminId,
          { userId: masterData?.userId },
        );
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  //#region reject company
  private async rejectCompany(masterData, updateData, content, approveById) {
    try {
      updateData.status.company = 2;
      updateData.dates.employment = new Date().getTime();
      updateData.rejection.company = content;
      updateData.companyAdminId = approveById;
      const id = masterData.empId;
      if (!id) return kInternalError;
      /// update Company entity
      const update = {
        companyVerification: '2',
        companyStatusApproveById: approveById,
      };
      const result = await this.salarySlipRepo.updateRowData(update, id);
      if (result == k500Error) return kInternalError;
    } catch (error) {}
  }

  //#region reject salary slip
  private async rejectSalarySlip(masterData, updateData, content, approveById) {
    try {
      updateData.status.salarySlip = 2;
      updateData.dates.employment = new Date().getTime();
      updateData.rejection.salarySlip = content;
      updateData.salarySlipAdminId = approveById;
      const id = masterData.salarySlipId;
      if (!id) return kInternalError;
      /// update salary slip entity
      const salaryVerifiedDate = this.typeService.getGlobalDate(new Date());
      const update: any = { salaryVerifiedDate, status: '2', approveById };
      update.rejectReason = content;
      const result = await this.salarySlipRepo.updateRowData(update, id);
      if (result == k500Error) return kInternalError;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region reject work mail
  private async rejectWorkMail(masterData, updateData, content, approveById) {
    try {
      updateData.status.workMail = 2;
      updateData.dates.employment = new Date().getTime();
      updateData.rejection.workMail = content;
      updateData.workMailAdminId = approveById;
      const id = masterData.workMailId;
      if (!id) return kInternalError;
      /// update work mail entity
      const verifiedDate = this.typeService.getGlobalDate(new Date());
      const update: any = { verifiedDate, status: '2', approveById };
      update.rejectReason = content;
      const result = await this.workMailRepo.updateRowData(update, id);
      if (result == k500Error) return kInternalError;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region select loan amount
  private async selectLoanAmount(updateData, loanId) {
    try {
      updateData.status.loan = -1;
      const update: any = { loanStatus: 'InProcess' };
      const result = await this.loanRepo.updateRowData(update, loanId);
      if (result == k500Error) return kInternalError;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //#region reject Bank
  private async rejectBank(masterData, updateData, content, approveById) {
    try {
      updateData.status.bank = 2;
      updateData.dates.banking = new Date().getTime();
      updateData.rejection.banking = content;
      const id = masterData?.loanData?.bankingId;
      if (!id) return kInternalError;
      /// update banking entity
      const update: any = {
        salaryVerification: '2',
        salaryVerificationDate: new Date().toJSON(),
        adminId: approveById,
      };
      update.rejectReason = content;
      const result = await this.bankingRepo.updateRowData(update, id);
      if (result == k500Error) return kInternalError;
    } catch (error) {}
  }

  // Get all pending verification count
  // Includes -> Employment, Banking, Residence, Selfie
  async pendingCount() {
    const key = 'ADMIN_VERIFICATION_COUNT';
    try {
      const existingData = await this.redisService.getKeyDetails(key);
      if (existingData) return JSON.parse(existingData);
    } catch (error) {}

    // Query preparation
    const attributes = [
      'stage',
      [Sequelize.fn('COUNT', Sequelize.col('stage')), 'count'],
    ];
    const options = {
      group: 'stage',
      where: {
        [Op.or]: [
          // Pending from admin
          { stageStatus: 0 },
          // Pending from user but nees to show in verification for EMandate and ESign
          {
            stage: {
              [Op.or]: [
                UserStage.MANDATE,
                UserStage.ESIGN,
                UserStage.LOAN_ACCEPT,
              ],
            },
          },
        ],
      },
    };

    // Query
    const usersListData = await this.userRepository.getTableWhereData(
      attributes,
      options,
    );
    if (usersListData == k500Error) throw new Error();

    const finalizedData = {
      banking: 0,
      employment: 0,
      selfie: 0,
      kyc: 0,
      finalApproval: 0,
      loanAccept: 0,
      eMandate: 0,
      eSign: 0,
      disbursement: 0,
      onHold: 0,
      total: 0,
    };
    usersListData.forEach((el) => {
      try {
        const stage = el.stage ?? '';
        if (stage == UserStage.EMPLOYMENT) {
          finalizedData.employment = +el.count;
          finalizedData.total += +el.count;
        } else if (stage == UserStage.BANKING) {
          finalizedData.banking = +el.count;
          finalizedData.total += +el.count;
        } else if (stage == UserStage.SELFIE) {
          finalizedData.selfie = +el.count;
          finalizedData.total += +el.count;
        } else if (stage == UserStage.PAN) {
          finalizedData.kyc = +el.count;
          finalizedData.total += +el.count;
        } else if (stage == UserStage.FINAL_VERIFICATION) {
          finalizedData.finalApproval = +el.count;
          finalizedData.total += +el.count;
        } else if (stage == UserStage.LOAN_ACCEPT) {
          finalizedData.loanAccept = +el.count;
          finalizedData.total += +el.count;
        } else if (stage == UserStage.MANDATE) {
          finalizedData.eMandate = +el.count;
          finalizedData.total += +el.count;
        } else if (stage == UserStage.ESIGN) {
          finalizedData.eSign = +el.count;
          finalizedData.total += +el.count;
        } else if (stage == UserStage.DISBURSEMENT) {
          finalizedData.disbursement = +el.count;
          finalizedData.total += +el.count;
        } else if (stage == UserStage.ON_HOLD) {
          finalizedData.onHold = +el.count;
          finalizedData.total += +el.count;
        }
      } catch (error) {}
    });

    let ttlInSeconds = 30;
    if (finalizedData.total >= 100) ttlInSeconds = 45;

    await this.redisService.updateKeyDetails(
      key,
      JSON.stringify(finalizedData),
      ttlInSeconds,
    );

    return finalizedData;
  }
  //#endregion

  // Employment verification list
  async employment(adminId) {
    const maskOptions = await this.sharedCommonService.findMaskRole(adminId);
    // Table joins
    const masterInclude: any = { model: MasterEntity };
    masterInclude.attributes = [
      'companyAdminId',
      'loanId',
      'salarySlipAdminId',
      'workMailAdminId',
      'otherInfo',
    ];
    masterInclude.include = { model: loanTransaction };
    masterInclude.include.attributes = ['id', 'assignTo'];
    const companyInclude: any = { model: employmentDetails };
    companyInclude.attributes = [
      'id',
      'companyName',
      'salarySlipId',
      'workMailId',
    ];
    const salaryInclude: any = {
      model: SalarySlipEntity,
    };
    salaryInclude.attributes = ['id', 'response', 'userId'];
    const include = [companyInclude, masterInclude, salaryInclude];

    // Query preparation
    const attributes = [
      'city',
      'completedLoans',
      'fullName',
      'id',
      'phone',
      'stageTime',
      'lastCrm',
    ];
    const options = {
      useMaster: false,
      include,
      where: { stage: UserStage.EMPLOYMENT, stageStatus: 0 },
      order: [[salaryInclude, 'id', 'DESC']],
    };

    // Query
    const userList = await this.userRepository.getTableWhereData(
      attributes,
      options,
    );

    if (userList == k500Error) return kInternalError;
    const totalCount = userList.length ?? 0;
    const preparedList = [];

    let savedAdmins = await this.redisService.get('ADMIN_LIST');
    if (savedAdmins) savedAdmins = JSON.parse(savedAdmins);
    let loanList = [];
    // Get last approved amount
    // const userIds = userList.map((el) => el.id);
    // const loanAttr = ['id', 'netApprovedAmount', 'userId', 'assignTo'];
    // const loanOptions = {
    //   order: [['id', 'DESC']],
    //   where: { loanStatus: 'Complete', userId: { [Op.in]: userIds } },
    // };
    // const loanList = await this.loanRepo.getTableWhereData(
    //   loanAttr,
    //   loanOptions,
    // );
    // if (loanList === k500Error) throw Error();

    for (let index = 0; index < totalCount; index++) {
      try {
        const userData = userList[index];
        if (!userData) continue;
        const lastCrm = userData?.lastCrm;
        const empData = userData.employmentData ?? {};
        const masterData = userData.masterData ?? {};
        const salaryData = userData.salaryData ?? [];
        const loanData = masterData.loanData ?? {};
        let missingDetails = '';
        let response =
          salaryData.length > 0
            ? JSON.parse(
                salaryData[0]?.response ? salaryData[0]?.response : null,
              ) ?? {}
            : {};
        let salaryDate;
        const currentDate = this.typeService.getGlobalDate(new Date());
        currentDate.setHours(0, 0, 0, 0);
        currentDate.setDate(
          currentDate.getDate() -
            GLOBAL_RANGES.MAX_ELIGIBLE_SALARY_DATE_IN_DAYS,
        );
        salaryDate = this.typeService.getGlobalDate(response?.SalaryPeriod);
        if (response?.document_type) {
          missingDetails += response?.document_type + ', ';
        }
        if (!response?.name || response?.name === null) {
          missingDetails += salaryMissingDetails.USER_NAME_MISMATCH + ', ';
        }
        if (!response?.companyName || response?.companyName === null) {
          missingDetails += salaryMissingDetails.COMPANY_NAME_MISMATCH + ', ';
        }
        if (!response?.netPayAmount || response?.netPayAmount === null) {
          missingDetails += salaryMissingDetails.PAY_AMOUNT_NOT_FOUND + ', ';
        }
        if (!response?.SalaryPeriod || response?.SalaryPeriod === null) {
          missingDetails += salaryMissingDetails.SALARY_PERIOD_NOT_FOUND;
        } else if (response?.SalaryPeriod && currentDate >= salaryDate) {
          missingDetails += salaryMissingDetails.SALARY_PERIOD_NOT_VALID;
        }
        if (missingDetails.endsWith(', '))
          missingDetails = missingDetails.slice(0, -2);
        const crmData = lastCrm
          ? {
              'CRM Date': this.dateService.readableDate(lastCrm?.createdAt),
              CRM: lastCrm?.statusName,
              Title: lastCrm?.titleName,
              Remark: lastCrm?.remark,
              Disposition: lastCrm?.dispositionName,
            }
          : {};
        const ExtraData = {};
        if (crmData?.CRM) ExtraData['Last CRM by'] = crmData;
        const diffInMinutes = this.typeService.dateDifference(
          new Date(),
          userData.stageTime,
          'Minutes',
        );

        let lastApprovedAmount = '-';
        let previousLoans = loanList.filter((el) => el.userId == userData.id);
        if (previousLoans.length > 0) {
          previousLoans = previousLoans.sort((b, a) => a.id - b.id);
          lastApprovedAmount = previousLoans[0].netApprovedAmount ?? 0;
        }
        const phone = this.cryptService.decryptPhone(userData?.phone) ?? '';
        const maskedPhone = maskOptions?.isMaskPhone
          ? this.cryptService.dataMasking('phone', phone)
          : phone;

        const preparedData = {
          'Difference in minutes': diffInMinutes,
          'Waiting time': this.getWaitingTime(diffInMinutes),
          Assign:
            (await this.sharedCommonService.getAdminData(loanData?.assignTo))
              ?.fullName ?? '-',
          'Loan Id': masterData.loanId ?? '-',
          Name: userData.fullName ?? '-',
          'Mobile number': maskedPhone,
          'Last CRM by': lastCrm?.adminName ?? '-',
          'Completed loans': userData.completedLoans ?? 0,
          'Last approved amount': lastApprovedAmount,
          'Employment information':
            masterData?.otherInfo?.employmentInfo === ''
              ? '-'
              : masterData?.otherInfo?.employmentInfo ?? '-',
          'Company name': empData?.companyName ?? '-',
          'Missing Details': missingDetails,
          City: userData?.city ?? '-',
          'Last action by': '-',
          userId: userData.id ?? '-',
          assignId: loanData?.assignTo ?? '-',
          employeeId: empData?.id,
          workMailId: empData?.workMailId,
          salarySlipId: empData?.salarySlipId,
          Status: '0',
          ExtraData,
        };
        const companyAdminId = masterData.companyAdminId;
        const salarySlipAdminId = masterData.salarySlipAdminId;
        const workMailAdminId = masterData.workMailAdminId;
        let targetAdminId;
        if (companyAdminId && companyAdminId != SYSTEM_ADMIN_ID)
          targetAdminId = companyAdminId;
        if (salarySlipAdminId && salarySlipAdminId != SYSTEM_ADMIN_ID)
          targetAdminId = salarySlipAdminId;
        if (workMailAdminId && workMailAdminId != SYSTEM_ADMIN_ID)
          targetAdminId = workMailAdminId;
        if (targetAdminId)
          preparedData['Last action by'] =
            (await this.sharedCommonService.getAdminData(targetAdminId))
              ?.fullName ?? '-';
        preparedList.push(preparedData);
      } catch (error) {}
    }

    return { count: totalCount, rows: preparedList };
  }
  //#endregion

  // Get detailed summry of particular user's employment
  async employmentDetails(reqData) {
    try {
      // Params validation
      const empId = reqData.empId;
      if (!empId) return kParamMissing('empId');
      // Query preparation
      const workMailInclude: any = { model: WorkMailEntity };
      workMailInclude.attributes = ['id', 'email', 'status'];
      const salarySlipIncldue: any = { model: SalarySlipEntity };
      salarySlipIncldue.attributes = [
        'id',
        'status',
        'url',
        'response',
        'rejectReason',
        'salarySlipDate',
      ];
      const include = [workMailInclude, salarySlipIncldue];
      const attributes = [
        'companyAddress',
        'companyPhone',
        'companyName',
        'companyUrl',
        'companyVerification',
      ];
      const options = { useMaster: false, include, where: { id: empId } };

      // Query
      const empData = await this.employementRepo.getRowWhereData(
        attributes,
        options,
      );
      if (!empData) return k422ErrorMessage(kNoDataFound);
      let missingDetails = [];
      const finalizedData: any = {
        companyAddress: empData.companyAddress ?? '',
        companyUrl: empData.companyUrl ?? '',
        companyPhone: empData.companyPhone ?? '',
        companyStatus: 'Pending',
        workMailStatus: 'Pending',
        workMail: '',
        salarySlipStatus: 'Skipped',
        verificationDocumentUrl: '',
        systemDeclineReason: missingDetails,
      };

      // Company
      const companyStatus = empData.companyVerification;
      if (['1', '3'].includes(companyStatus))
        finalizedData.companyStatus = 'Approved';
      if (companyStatus == '2') finalizedData.companyStatus = 'Rejected';

      // Work mail
      const workMailData = empData.workMail ?? {};
      if (['1', '3', '4'].includes(workMailData.status))
        finalizedData.workMailStatus = 'Approved';
      if (workMailData.status == '2') finalizedData.workMailStatus = 'Rejected';
      if (workMailData.email) finalizedData.workMail = workMailData.email;
      else if (workMailData.status == '4') finalizedData.workMail = 'Skipped';

      //work mail and companyUrl mismatch check
      if (
        finalizedData.workMail != 'Skipped' &&
        finalizedData.companyUrl &&
        finalizedData.workMail
      ) {
        const email = finalizedData.workMail;
        const companyUrl = finalizedData.companyUrl;
        const idxOfAtSign = email.indexOf('@');
        let companyNameInEmail = email.slice(
          idxOfAtSign + 1,
          email.indexOf('.', idxOfAtSign + 1),
        );
        let companyNameInUrl;
        if (companyUrl.includes('www')) {
          const idxOfWww = companyUrl.indexOf('wwww');
          companyNameInUrl = companyUrl.slice(
            idxOfWww + 4,
            email.indexOf('.', idxOfWww + 4),
          );
        } else {
          const idxOfDoubleSlash = companyUrl.indexOf('://');
          companyNameInUrl = companyUrl.slice(
            idxOfDoubleSlash + 3,
            email.indexOf('.', idxOfDoubleSlash + 3),
          );
        }

        companyNameInEmail = companyNameInEmail.toLowerCase();
        companyNameInUrl = companyNameInUrl.toLowerCase();

        const isMatch =
          companyNameInEmail.includes(companyNameInUrl) ||
          companyNameInUrl.includes(companyNameInEmail);
        if (!isMatch) {
          missingDetails.push(salaryMissingDetails.COMPANY_NAME_MISMATCH);
          finalizedData.editCompanyName = 'true';
        }
      }

      // Salary slip
      const salarySlipData = empData?.salarySlip;
      if (salarySlipData) {
        finalizedData.salarySlipStatus = 'Pending';
        finalizedData.verificationDocumentUrl = salarySlipData?.url ?? '-';

        if (['1', '3', '4'].includes(salarySlipData?.status))
          finalizedData.salarySlipStatus = 'Approved';
        if (salarySlipData?.status == '2')
          finalizedData.salarySlipStatus = 'Rejected';

        let response = JSON.parse(salarySlipData?.response);

        if (response?.Type === 'Offer Letter') {
          finalizedData.type = 'Offer Letter';
        } else if (response?.Type === 'BankStatement') {
          finalizedData.type = 'Bank Statement';
          if (response?.bank_name) finalizedData.bankName = response.bank_name;
        } else if (response?.Type === 'Invalid document') {
          finalizedData.type = 'Invalid document';
        } else if (response?.Type === 'Salary Slip') {
          finalizedData.type = 'Salary Slip';
        } else {
          finalizedData.type = 'Other Document';
        }

        let salaryDate;
        const currentDate = this.typeService.getGlobalDate(new Date());
        currentDate.setHours(0, 0, 0, 0);
        currentDate.setDate(
          currentDate.getDate() -
            GLOBAL_RANGES.MAX_ELIGIBLE_SALARY_DATE_IN_DAYS,
        );
        salaryDate = this.typeService.getGlobalDate(response?.SalaryPeriod);
        if (response?.document_type) {
          missingDetails.push(response?.document_type);
        }
        if (!response?.name || response?.name === null) {
          missingDetails.push(salaryMissingDetails.USER_NAME_MISMATCH);
        }
        if (!response?.companyName || response?.companyName === null) {
          missingDetails.push(salaryMissingDetails.COMPANY_NAME_MISMATCH);
          finalizedData.editCompanyName = 'true';
        }
        if (!response?.netPayAmount || response?.netPayAmount === null) {
          missingDetails.push(salaryMissingDetails.PAY_AMOUNT_NOT_FOUND);
        }
        if (!response?.SalaryPeriod || response?.SalaryPeriod === null) {
          missingDetails.push(salaryMissingDetails.SALARY_PERIOD_NOT_FOUND);
        } else if (response?.SalaryPeriod && currentDate >= salaryDate) {
          missingDetails.push(salaryMissingDetails.SALARY_PERIOD_NOT_VALID);
        }
      }

      return finalizedData;
    } catch (error) {}
  }

  async banking(query) {
    try {
      const adminid = query.adminid;
      const status = query?.status ?? '0';
      const attributes = [
        'id',
        'accountNumber',
        'ifsCode',
        'assignedTo',
        'bank',
        'loanId',
        'userId',
        'salary',
        'salaryDate',
        'attempts',
        'adminId',
        'otherDetails',
        'rejectReason',
        'salaryVerification',
        'salaryVerificationDate',
        'createdAt',
        'updatedAt',
        'nameMissMatch',
        'additionalNameMissMatch',
        'additionalNameSimilarity',
        'name',
        'nameSimilarity',
        'consentMode',
        'aaDataStatus',
        'isSuspicious',
        'bankStatement',
        'stmtStartDate',
        'stmtEndDate',
        'additionalBankStatement',
        'addStmtStartDate',
        'addStmtEndDate',
        'additionalURLs',
        'additionalURLsDates',
        'additionalBank',
        'additionalName',
      ];
      const options: any = await this.bankingOptions(query);
      if (options?.message) return options;
      // Query
      const bankData = await this.bankingRepo.getTableWhereDataWithCounts(
        attributes,
        options,
      );
      if (bankData === k500Error) throw new Error();
      return await this.prepareBankData(bankData, status, adminid);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async bankingOptions(query) {
    try {
      //  Pending = '0', Approved = '1',  Decline = '2', All = '4', LoanDecline = '6'
      const status = query?.status ?? '0';
      // newOrRepeated (0 = Repeated, 1 = New)
      const newOrRepeated = query?.newOrRepeated;
      let searchText = query?.searchText;
      const download = query?.download ?? 'false';
      const page = query?.page ?? 1;
      const adminId = query?.adminId;
      const startDate = query?.startDate ?? new Date();
      const endDate = query?.endDate ?? new Date();
      const range = this.typeService.getUTCDateRange(startDate, endDate);
      const dateRange = { [Op.gte]: range.fromDate, [Op.lte]: range.endDate };
      const bankWhere: any = {};
      let userWhere: any;
      if (searchText) {
        const firstTwoCh = searchText.substring(0, 2).toLowerCase();
        if (firstTwoCh == 'l-') bankWhere.loanId = +searchText.substring(2);
        else {
          userWhere = {};
          if (!isNaN(searchText)) {
            searchText = this.cryptService.encryptPhone(searchText);
            searchText = searchText.split('===')[1];
            userWhere.phone = { [Op.iRegexp]: searchText };
          } else
            userWhere[Op.or] = [
              { fullName: { [Op.iRegexp]: searchText } },
              { email: { [Op.iRegexp]: searchText } },
            ];
        }
      }
      if (newOrRepeated) {
        userWhere = userWhere ? userWhere : {};
        if (newOrRepeated == '1') userWhere.completedLoans = 0;
        else if (newOrRepeated == '0')
          userWhere.completedLoans = { [Op.gt]: 0 };
      }
      // Query preparation
      const empInclude: any = { model: employmentDetails };
      empInclude.attributes = ['companyName', 'salary', 'salaryDate'];
      const cibilInc = {
        model: CibilScoreEntity,
        attributes: ['id', 'status', 'userId', 'cibilScore', 'plScore'],
        required: false,
      };
      const masterInclude: any = { model: MasterEntity };
      masterInclude.attributes = ['otherInfo'];
      const userInclude: any = { model: registeredUsers };
      userInclude.attributes = [
        'id',
        'city',
        'completedLoans',
        'fullName',
        'phone',
        'stageTime',
        'stageStatus',
        'stage',
        'lastCrm',
        'pinCrm',
      ];
      if (userWhere) userInclude.where = userWhere;
      userInclude.include = [empInclude, masterInclude];
      const loanInc: any = {
        model: loanTransaction,
        attributes: ['id', 'assignTo', 'bankAttempts'],
        where: {},
        include: [cibilInc],
      };
      if (status == '0') loanInc.where.loanStatus = 'InProcess';
      if (status == '2') loanInc.where.loanStatus = { [Op.ne]: 'Rejected' };
      else if (status == '6') loanInc.where.loanStatus = 'Rejected';
      const include = [userInclude, loanInc];
      if (status == '0') {
        if (adminId) bankWhere.assignedTo = adminId;
        return {
          where: {
            ...bankWhere,
            salaryVerification: status,
            [Op.and]: [
              Sequelize.literal(`"user"."stage" = ${UserStage.BANKING}`),
            ],
          },
          include,
          order: [['id', 'DESC']],
        };
      }

      let salSatus = status;
      if (status == '1') salSatus = ['3'];
      else if (status == '2' || status == '6') salSatus = ['2'];
      else if (status == '4') salSatus = ['0', '2', '3'];
      if (!Array.isArray(salSatus)) salSatus = [salSatus];
      if (adminId) bankWhere.adminId = adminId;
      else bankWhere.adminId = { [Op.ne]: SYSTEM_ADMIN_ID };
      let bankId = [];
      if (status != '4') {
        const bankAttr: any = [
          [Sequelize.fn('MAX', Sequelize.col('id')), 'id'],
          'loanId',
        ];
        const bankOpts = {
          useMaster: false,
          where: {
            salaryVerification: { [Op.in]: salSatus },
            salaryVerificationDate: dateRange,
            ...bankWhere,
          },
          group: ['loanId'],
          order: [[Sequelize.literal('"id" DESC')]],
        };
        const bankList = await this.bankingRepo.getTableWhereData(
          bankAttr,
          bankOpts,
        );
        if (bankList === k500Error) throw new Error();
        bankId = bankList.map((b) => b?.id);
      }

      const opts: any = {
        useMaster: false,
        where: { id: bankId },
        include,
        order: [['id', 'DESC']],
      };
      if (status == '4')
        opts.where = {
          salaryVerification: { [Op.or]: salSatus },
          salaryVerificationDate: dateRange,
          ...bankWhere,
        };
      if (download != 'true') {
        opts.offset = page * PAGE_LIMIT - PAGE_LIMIT;
        opts.limit = PAGE_LIMIT;
      }
      return opts;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // prepare data
  async prepareBankData(bankData, status, adminid) {
    const maskOptions = await this.sharedCommonService.findMaskRole(adminid);
    let loanList: any = [];
    // if (status == '0') {
    //   // Get last approved amount
    //   const totalUserIds = [...new Set(bankData.rows.map((el) => el.userId))];
    //   const loanAttr = ['id', 'netApprovedAmount', 'userId'];
    //   const loanOptions = {
    //     where: {
    //       id: {
    //         [Op.in]: Sequelize.literal(
    //           `(SELECT MAX(id) FROM "loanTransactions" WHERE "loanStatus" = 'Complete' AND "userId" IN (:totalUserIds) GROUP BY "userId")`,
    //         ),
    //       },
    //     },
    //     order: [['id', 'DESC']],
    //     replacements: { totalUserIds },
    //   };
    //   loanList = await this.loanRepo.getTableWhereData(loanAttr, loanOptions);
    //   if (loanList === k500Error) throw Error();
    // }
    const length = bankData.rows.length;
    const preparedList = [];
    const userIds = [];
    for (let index = 0; index < length; index++) {
      try {
        const bank = bankData.rows[index];
        const bankStmtObj: any = {
          url: bank?.bankStatement ?? '-',
          stmtStartDate: bank?.stmtStartDate
            ? this.typeService.getDateFormated(bank.stmtStartDate, '/')
            : '-',
          stmtEndDate: bank?.stmtEndDate
            ? this.typeService.getDateFormated(bank.stmtEndDate, '/')
            : '-',
          bankCode: bank?.bank ?? '-',
        };
        const finalAdditionalURLs =
          this.bankingService.manageNGetAdditionalUrls(
            bank?.additionalURLsDates,
            bank?.additionalURLs,
          );
        if (bank.additionalBankStatement) {
          const additionalBankStatementObj = {
            url: bank?.additionalBankStatement ?? '-',
            stmtStartDate: bank?.addStmtStartDate
              ? this.typeService.getDateFormated(bank.addStmtStartDate, '/')
              : '-',
            stmtEndDate: bank?.addStmtEndDate
              ? this.typeService.getDateFormated(bank.addStmtEndDate, '/')
              : '-',
            bankCode: bank?.additionalBank ?? '-',
          };
          finalAdditionalURLs.push(additionalBankStatementObj);
        }
        finalAdditionalURLs.sort((a, b) => {
          const dateFormat = (ele) => {
            let date = ele.includes('/') ? ele.split('/') : ele.split('-');
            return new Date(date.reverse().join('/'));
          };
          let dateA = dateFormat(a.stmtEndDate);
          let dateB = dateFormat(b.stmtEndDate);
          return dateB.getTime() - dateA.getTime();
        });
        const user = bank.user ?? {};
        // Temporary solution
        if (status === '0' && user.stageStatus != 0) continue;
        const emp = user?.employmentData ?? {};
        const master = user?.masterData ?? {};
        const loan = bank?.loanData ?? {};
        const cibil = loan?.cibilData ?? {};
        if (userIds.includes(user.id) && status == '0') continue;
        let isCibilError = false;
        if (cibil?.status == '2' || cibil?.status == '3') isCibilError = true;
        const lastCrm = user?.lastCrm;
        const bankStatus = bank?.salaryVerification;
        const updatedAt = bank?.salaryVerificationDate ?? bank?.updatedAt;
        const createdAt = this.typeService.getDateFormatted(bank?.createdAt);
        const lastUpdate = this.typeService.getDateFormatted(updatedAt);
        const preparedData: any = {};
        const ExtraData = {};
        if (status == '0') {
          const diffInMinutes = this.typeService.dateDifference(
            new Date(),
            user.stageTime,
            'Minutes',
          );
          preparedData['Difference in minutes'] = diffInMinutes;
          preparedData['Waiting time'] = this.getWaitingTime(diffInMinutes);

          const crmData = lastCrm
            ? {
                'CRM Date': this.dateService.readableDate(lastCrm?.createdAt),
                CRM: lastCrm?.statusName,
                Title: lastCrm?.titleName,
                Remark: lastCrm?.remark,
                Disposition: lastCrm?.dispositionName,
              }
            : {};
          if (crmData?.CRM) ExtraData['Last CRM by'] = crmData;
        }
        preparedData['Suspicious'] = {
          SUSPICIOUS_STATEMENT: loan?.bankAttempts?.count ?? 0,
        };
        preparedData['Assign'] =
          (await this.sharedCommonService.getAdminData(loan?.assignTo))
            ?.fullName ?? '-';
        preparedData['Loan id'] = bank.loanId ?? '-';
        preparedData['Name'] = user.fullName ?? '-';
        const phone = this.cryptService.decryptPhone(user?.phone) ?? '-';
        const maskedPhone = maskOptions?.isMaskPhone
          ? this.cryptService.dataMasking('phone', phone)
          : phone;
        preparedData['Mobile number'] = maskedPhone;
        if (status == '0') {
          let lastApprovedAmount: any = '-';
          let previousLoans = loanList.filter((el) => el.userId == user.id);
          if (previousLoans.length > 0) {
            previousLoans = previousLoans.sort((b, a) => a.id - b.id);
            lastApprovedAmount = +(previousLoans[0]?.netApprovedAmount ?? 0);
          }
          preparedData['Last approved amount'] = lastApprovedAmount;
          preparedData['Last CRM by'] = lastCrm?.adminName ?? '-';
        }
        preparedData['Completed loans'] = user?.completedLoans ?? '0';
        preparedData['Employment information'] =
          master?.otherInfo?.employmentInfo === ''
            ? '-'
            : master?.otherInfo?.employmentInfo ?? '-';
        preparedData['Applied bank'] = bank.bank ?? '-';
        const accountNumber = bank?.accountNumber ?? '-';
        const maskedAccount = maskOptions?.isDisbursementAccMask
          ? this.cryptService.dataMasking('bankAccount', accountNumber)
          : accountNumber;
        preparedData['Account number'] = maskedAccount;
        preparedData['IFSC code'] = bank.ifsCode ?? '-';
        preparedData['Company name'] = emp.companyName ?? '-';
        preparedData['Salary'] = emp.salary ?? '-';
        preparedData['Statement'] = bank?.bankStatement ? bankStmtObj : '-';
        preparedData['Source'] =
          bank?.aaDataStatus != 3 &&
          bank?.bankStatement != null &&
          !bank?.consentMode
            ? 'BANKINGPRO'
            : bank?.consentMode;
        preparedData['Additional Urls'] = finalAdditionalURLs;
        preparedData['City'] = user?.city ?? '-';
        preparedData['Created date'] = createdAt;
        if (status != '0')
          preparedData[
            status == '1'
              ? 'Approved by'
              : status == '2'
              ? 'Rejected by'
              : 'Last action by'
          ] =
            (await this.sharedCommonService.getAdminData(bank?.adminId))
              ?.fullName ?? '-';
        preparedData[
          status == '0'
            ? 'Last updated'
            : status == '2' || status == '6'
            ? 'Reject date'
            : 'Verified date'
        ] = lastUpdate;
        if (status == '2' || status == '6') {
          preparedData['Reject reason'] = bank?.rejectReason ?? '-';
          preparedData['Reject counts'] = bank?.attempts ?? '-';
        }
        preparedData['bankingId'] = bank.id ?? '-';
        preparedData['userId'] = user.id ?? '-';
        preparedData['assignId'] = loan?.assignTo ?? '-';
        preparedData['userSalaryDate'] = emp?.salaryDate ?? '-';
        preparedData['systemDate'] = bank?.salaryDate ?? '-';
        preparedData['actualSalary'] = bank?.salary ?? '-';
        preparedData['Status'] = bankStatus;
        preparedData['ExtraData'] = ExtraData;
        if (status == '0')
          preparedData['salary_list'] = bank?.otherDetails?.salary ?? {};
        preparedData['isCibilError'] = isCibilError;
        preparedData['nameMissMatch'] = bank?.nameMissMatch == 0 ? true : '-';
        preparedData['additionalNameMissMatch'] =
          bank?.additionalNameMissMatch == 0 ? true : '-';
        preparedData['additionalAccountName'] = bank?.additionalName ?? '-';
        preparedData['additionalNameSimilarity'] =
          bank?.additionalNameSimilarity ?? '-';
        preparedData['accountName'] = bank?.name ?? '-';
        preparedData['nameSimilarity'] = bank?.nameSimilarity ?? '-';
        preparedData['isSuspicious'] = bank?.isSuspicious == 1 ? true : false;
        preparedData['ECS bounce count'] =
          bank?.otherDetails?.ecsDetails?.ecsBounceCount ?? 0;
        /// Pin CRM for [Verification -> Bank Statements] (Show pin after user's name)
        preparedData['pinCRM'] = null;
        if (bank?.loanId == user?.pinCrm?.loanId) {
          preparedData['pinCRM'] = {
            pinAdminName: user?.pinCrm?.adminName ?? '-',
            pinDescription: user?.pinCrm?.remark ?? '-',
            pinCreatedAt: user?.pinCrm?.createdAt ?? '-',
            pinAdminRole: user?.pinCrm?.adminRole ?? '-',
          };
        }
        preparedList.push(preparedData);
        userIds.push(user.id);
      } catch (error) {}
    }
    if (status == '0')
      return { count: preparedList.length, rows: preparedList };
    bankData.rows = preparedList;
    return bankData;
  }

  async reFetchBankVerification(reqData) {
    const targetList = await this.banking({});
    const targetLoanIds = reqData.targetLoanIds ?? [];

    const rows = targetList.rows ?? [];

    const loanIds = rows.map((el) => el['Loan id']);

    const bankingInclude: SequelOptions = { model: BankingEntity };
    bankingInclude.attributes = ['accountDetails', 'bankStatement'];
    bankingInclude.where = {
      accountDetails: { [Op.ne]: null },
      salaryVerification: '0',
    };
    const include = [bankingInclude];
    const loanAttr = ['id', 'userId'];
    const loanOptions = {
      include,
      where: { id: loanIds, loanStatus: 'InProcess' },
    };

    const loanList = await this.repoManager.getTableWhereData(
      loanTransaction,
      loanAttr,
      loanOptions,
    );
    if (loanList == k500Error) throw new Error();

    for (let index = 0; index < loanList.length; index++) {
      try {
        const loanData = loanList[index];
        const bankingData = loanData?.bankingData ?? {};

        const loanId = loanData.id;
        if (targetLoanIds.length > 0) {
          if (!targetLoanIds.includes(loanId)) continue;
        }

        const body = {
          accountDetails: JSON.parse(bankingData.accountDetails),
          filePath: bankingData.bankStatement,
          userId: loanData.userId,
        };

        const result = await this.sharedbanking.validateEligibility(body);
      } catch (error) {}
    }

    return {};
  }

  async selfie(adminId) {
    try {
      const maskOptions = await this.sharedCommonService.findMaskRole(adminId);
      // Query preparation
      const kycInclude: { model; attributes? } = { model: KYCEntity };
      kycInclude.attributes = ['profileImage'];
      const masterInclude: { model; attributes?; include? } = {
        model: MasterEntity,
      };
      masterInclude.attributes = ['loanId', 'otherInfo'];
      masterInclude.include = { model: loanTransaction };
      masterInclude.include.attributes = ['id', 'assignTo'];
      const selfieInclude: { model; attributes? } = { model: UserSelfieEntity };
      selfieInclude.attributes = [
        'id',
        'image',
        'response',
        'tempImage',
        'adminId',
      ];
      const include = [kycInclude, masterInclude, selfieInclude];
      const attributes = [
        'city',
        'completedLoans',
        'fullName',
        'id',
        'phone',
        'stageTime',
        'lastCrm',
        'pinCrm',
      ];
      const options = {
        include,
        where: { stage: UserStage.SELFIE, stageStatus: 0 },
      };

      // Query
      const userList = await this.userRepository.getTableWhereData(
        attributes,
        options,
      );
      if (userList == k500Error) return kInternalError;

      // Fine tuning
      const preparedList = [];
      for (let index = 0; index < userList.length; index++) {
        try {
          const userData = userList[index];
          const kycData = userData.kycData ?? {};
          const masterData = userData.masterData ?? {};
          const selfieData = userData.selfieData ?? {};
          const lastCrm = userData?.lastCrm;
          const loanData = masterData?.loanData ?? {};
          const crmData = lastCrm
            ? {
                'CRM Date': this.dateService.readableDate(lastCrm?.createdAt),
                CRM: lastCrm?.statusName,
                Title: lastCrm?.titleName,
                Remark: lastCrm?.remark,
                Disposition: lastCrm?.dispositionName,
              }
            : {};
          const ExtraData = {};
          if (crmData?.CRM) ExtraData['Last CRM by'] = crmData;
          let matchPer: any = 0;
          let confidence = 0;
          try {
            const response = selfieData?.response
              ? JSON.parse(selfieData?.response)
              : {};
            if (response?.SourceImageFace) {
              const simlarity = Math.round(
                response?.FaceMatches[0]?.Similarity ?? 0,
              );
              confidence = Math.round(
                response?.SourceImageFace?.Confidence ?? 0,
              );
              const facaMatch = response?.FaceMatches?.length > 0;
              if (facaMatch || simlarity) matchPer = simlarity;
            }
          } catch (error) {}

          const diffInMinutes = this.typeService.dateDifference(
            new Date(),
            userData.stageTime,
            'Minutes',
          );
          const phone = this.cryptService.decryptPhone(userData?.phone) ?? '-';
          const maskedPhone = maskOptions?.isMaskPhone
            ? this.cryptService.dataMasking('phone', phone)
            : phone;
          const preparedData: any = {
            'Difference in minutes': diffInMinutes,
            'Waiting time': this.getWaitingTime(diffInMinutes),
            Assign:
              (await this.sharedCommonService.getAdminData(loanData?.assignTo))
                ?.fullName ?? '-',
            'Loan id': masterData.loanId ?? '-',
            Name: userData.fullName ?? '-',
            'Mobile number': maskedPhone,
            'Last CRM by': lastCrm?.adminName ?? '-',
            'Completed loans': userData.completedLoans ?? 0,
            'Employment information':
              masterData?.otherInfo?.employmentInfo === ''
                ? '-'
                : masterData?.otherInfo?.employmentInfo ?? '-',
            City: userData.city ?? '-',
            'Profile image': selfieData.image
              ? decodeURIComponent(selfieData.image)
              : null,
            Profile_tempImg: selfieData.tempImage
              ? decodeURIComponent(selfieData.tempImage)
              : null,
            Aadhar_image: kycData.profileImage
              ? decodeURIComponent(kycData.profileImage)
              : null,
            Similarity: matchPer,
            Confidence: confidence,
            'Last action by': selfieData?.adminId
              ? (
                  await this.sharedCommonService.getAdminData(
                    selfieData?.adminId,
                  )
                )?.fullName
              : '-',
            userId: userData.id ?? '-',
            assignId: loanData?.assignTo ?? '-',
            selfieId: selfieData.id ?? '-',
            Status: '0',
            ExtraData,
          };
          /// Pin CRM for [Verification -> Selfie] (Show pin after user's name)
          preparedData['pinCRM'] = null;
          if (masterData?.loanId == userData?.pinCrm?.loanId) {
            preparedData['pinCRM'] = {
              pinAdminName: userData?.pinCrm?.adminName ?? '-',
              pinDescription: userData?.pinCrm?.remark ?? '-',
              pinCreatedAt: userData?.pinCrm?.createdAt ?? '-',
              pinAdminRole: userData?.pinCrm?.adminRole ?? '-',
            };
          }
          preparedList.push(preparedData);
        } catch (error) {}
      }

      return { rows: preparedList, count: preparedList.length };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async kyc(adminId) {
    try {
      const maskOptions = await this.sharedCommonService.findMaskRole(adminId);
      // Query preparation
      const kycInclude: { model; attributes? } = { model: KYCEntity };
      kycInclude.attributes = [
        'aadhaarFront',
        'aadhaarBack',
        'aadhaarResponse',
        'aadhaarVerifiedAdmin',
        'maskedAadhaar',
        'pan',
        'panStatus',
        'panCardNumber',
        'panResponse',
        'nameSimilarity',
      ];
      const masterInclude: { model; attributes?; include? } = {
        model: MasterEntity,
      };
      masterInclude.attributes = ['loanId', 'otherInfo'];
      masterInclude.include = { model: loanTransaction };
      masterInclude.include.attributes = ['id', 'assignTo'];
      const include = [kycInclude, masterInclude];
      const attributes = [
        'city',
        'fullName',
        'id',
        'phone',
        'stageTime',
        'totalContact',
        'lastCrm',
        'completedLoans',
        'pinCrm',
      ];
      const options = {
        include,
        where: { stage: UserStage.PAN, stageStatus: 0 },
      };

      // Query
      const userList = await this.userRepository.getTableWhereData(
        attributes,
        options,
      );
      if (userList == k500Error) return kInternalError;
      let savedAdmins = await this.redisService.get('ADMIN_LIST');
      if (savedAdmins) savedAdmins = JSON.parse(savedAdmins);

      // Fine tuning
      const preparedList = [];
      for (let index = 0; index < userList.length; index++) {
        try {
          const userData = userList[index];
          const kycData = userData.kycData ?? {};
          const masterData = userData.masterData ?? {};
          const lastCrm = userData?.lastCrm;
          const loanData = masterData?.loanData ?? {};
          const crmData = lastCrm
            ? {
                'CRM Date': this.dateService.readableDate(lastCrm?.createdAt),
                CRM: lastCrm?.statusName,
                Title: lastCrm?.titleName,
                Remark: lastCrm?.remark,
                Disposition: lastCrm?.dispositionName,
              }
            : {};
          const ExtraData = {};
          if (crmData?.CRM) ExtraData['Last CRM by'] = crmData;
          let aadhaarResponse: any = {};
          let panName = '';
          if (kycData?.panResponse) {
            let panResponse = JSON.parse(kycData?.panResponse);
            if (typeof panResponse === 'string')
              panResponse = JSON.parse(panResponse);
            const document_status = panResponse?.document_status;
            const finalResponse: any = document_status ?? panResponse?.result;
            panName =
              finalResponse && finalResponse.length > 0
                ? finalResponse[0]?.validated_data?.full_name ?? '-'
                : finalResponse?.user_full_name ?? '-';
          }
          if (kycData.aadhaarResponse)
            aadhaarResponse = JSON.parse(kycData.aadhaarResponse);
          const diffInMinutes = this.typeService.dateDifference(
            new Date(),
            userData.stageTime,
            'Minutes',
          );
          const phone = this.cryptService.decryptPhone(userData?.phone) ?? '-';
          const maskedPhone = maskOptions?.isMaskPhone
            ? this.cryptService.dataMasking('phone', phone)
            : phone;

          const pan = kycData.panCardNumber ?? '-';
          const maskedPan = maskOptions?.isMaskPan
            ? this.cryptService.dataMasking('pan', pan)
            : pan;
          const preparedData: any = {
            'Difference in minutes': diffInMinutes,
            'Waiting time': this.getWaitingTime(diffInMinutes),
            Assign:
              (await this.sharedCommonService.getAdminData(loanData?.assignTo))
                ?.fullName ?? '-',
            'Loan id': masterData?.loanId ?? '-',
            Name: userData.fullName ?? '-',
            Approve: userData.kycData?.nameSimilarity > 0 ? true : false,
            'Mobile number': maskedPhone,
            'Last CRM by': lastCrm?.adminName ?? '-',
            'Completed loans': userData.completedLoans ?? 0,
            'Employment information':
              masterData?.otherInfo?.employmentInfo === ''
                ? '-'
                : masterData?.otherInfo?.employmentInfo ?? '-',
            City: userData.city ?? '-',
            'Last action by':
              (
                await this.sharedCommonService.getAdminData(
                  kycData.aadhaarVerifiedAdmin,
                )
              )?.fullName ?? '-',
            aadhaarFront: kycData.aadhaarFront,
            aadhaarBack: kycData.aadhaarBack,
            aadhaarNumber: kycData.maskedAadhaar ?? '-',
            'Aadhaar name':
              aadhaarResponse?.full_name ??
              aadhaarResponse?.name ??
              aadhaarResponse?.localResName ??
              '-',
            pan: kycData.pan,
            panCardNumber: maskedPan,
            'Pan name': panName,
            panStatus: kycData?.panStatus ?? '-',
            userId: userData.id ?? '-',
            assignId: loanData?.assignTo ?? '-',
            Status: '0',
            ExtraData,
          };
          /// Pin CRM for [Verification -> KYC] (Show pin after user's name)
          preparedData['pinCRM'] = null;
          if (masterData?.loanId == userData?.pinCrm?.loanId) {
            preparedData['pinCRM'] = {
              pinAdminName: userData?.pinCrm?.adminName ?? '-',
              pinDescription: userData?.pinCrm?.remark ?? '-',
              pinCreatedAt: userData?.pinCrm?.createdAt ?? '-',
              pinAdminRole: userData?.pinCrm?.adminRole ?? '-',
            };
          }
          preparedList.push(preparedData);
        } catch (error) {}
      }
      return { rows: preparedList, count: preparedList.length };
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async finalApproval(query) {
    const adminid = query?.adminid;
    const status = query?.status ?? '0';
    const attributes = [
      'id',
      'userId',
      'remark',
      'assignTo',
      'updatedAt',
      'loanAmount',
      'verifiedDate',
      'approvedReason',
      'netApprovedAmount',
      'manualVerification',
      'manualVerificationAcceptId',
      'companyId',
      'eligibilityDetails',
    ];
    const options: any = await this.finalApprovalOptions(query);
    if (options?.message) return options;
    // Query
    const loanData = await this.loanRepo.getTableWhereDataWithCounts(
      attributes,
      options,
    );

    if (loanData === k500Error) throw new Error();
    const comapnyIds = loanData.rows.map((item) => item.companyId);

    const companyAttr = ['id', 'companyName'];
    const companyOptions = { where: { id: { [Op.in]: comapnyIds } } };
    const companyData = await this.repoManager.getTableWhereData(
      GoogleCompanyResultEntity,
      companyAttr,
      companyOptions,
    );

    return await this.prepareFinalApproval(
      loanData,
      companyData,
      status,
      adminid,
    );
  }

  async finalApprovalOptions(query) {
    try {
      //  Pending = '0', Approved = '1',  LoanDecline = '2'
      let status = query?.status ?? '0';
      status = status == '1' ? '3' : status;
      // newOrRepeated (0 = Repeated, 1 = New)
      const newOrRepeated = query?.newOrRepeated;
      const adminId = query?.adminId;
      let searchText = query?.searchText;
      const download = query?.download ?? 'false';
      const page = query?.page ?? 1;
      const startDate = this.typeService.getGlobalDate(
        query?.startDate ?? new Date(),
      );
      const endDate = this.typeService.getGlobalDate(
        query?.endDate ?? new Date(),
      );
      let loanWhere: any = {};
      let userWhere: any;
      if (searchText) {
        const firstTwoCh = searchText.substring(0, 2).toLowerCase();
        if (firstTwoCh == 'l-') loanWhere.id = +searchText.substring(2);
        else {
          userWhere = {};
          if (!isNaN(searchText)) {
            searchText = this.cryptService.encryptPhone(searchText);
            searchText = searchText.split('===')[1];
            userWhere.phone = { [Op.iRegexp]: searchText };
          } else
            userWhere[Op.or] = [
              { fullName: { [Op.iRegexp]: searchText } },
              { email: { [Op.iRegexp]: searchText } },
            ];
        }
      }
      if (newOrRepeated) {
        userWhere = userWhere ? userWhere : {};
        if (newOrRepeated == '1') userWhere.completedLoans = 0;
        else if (newOrRepeated == '0')
          userWhere.completedLoans = { [Op.gt]: 0 };
      }
      // Query preparation
      const bankInc: any = { model: BankingEntity };
      bankInc.attributes = [
        'salary',
        'adminSalary',
        'otherDetails',
        'genderVerified',
      ];
      const masterInc: any = { model: MasterEntity };
      masterInc.attributes = ['otherInfo'];
      const predictionInclude: any = { model: PredictionEntity };
      predictionInclude.attributes = ['CFLScore', 'manualReason', 'reason'];
      const userInc: any = { model: registeredUsers };
      userInc.attributes = [
        'id',
        'city',
        'completedLoans',
        'fullName',
        'phone',
        'stageTime',
        'stageStatus',
        'stage',
        'lastCrm',
        'pinCrm',
      ];
      if (userWhere) userInc.where = userWhere;
      const cibilInc = {
        model: CibilScoreEntity,
        attributes: ['id', 'status', 'cibilScore', 'plScore'],
        required: false,
      };
      const include = [
        masterInc,
        userInc,
        bankInc,
        cibilInc,
        predictionInclude,
      ];
      if (status == '0') {
        if (adminId) loanWhere.assignTo = adminId;
        loanWhere.manualVerification = '0';
        return {
          where: {
            ...loanWhere,
            [Op.and]: [
              Sequelize.literal(
                `"registeredUsers"."stage" = ${UserStage.FINAL_VERIFICATION}`,
              ),
            ],
          },
          include,
          order: [['id', 'DESC']],
        };
      }
      if (adminId) loanWhere.manualVerificationAcceptId = adminId;
      else loanWhere.manualVerificationAcceptId = { [Op.ne]: SYSTEM_ADMIN_ID };
      const manualVerification =
        status == '4' ? { [Op.in]: ['0', '3', '2'] } : status;
      const opts: any = {
        where: {
          ...loanWhere,
          manualVerification,
          verifiedDate: {
            [Op.gte]: startDate.toJSON(),
            [Op.lte]: endDate.toJSON(),
          },
        },
        include,
        order: [['id', 'DESC']],
      };
      if (download != 'true') {
        opts.offset = page * PAGE_LIMIT - PAGE_LIMIT;
        opts.limit = PAGE_LIMIT;
      }
      return opts;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // prepare data
  async prepareFinalApproval(loanData, companyData, status, adminid) {
    const maskOptions = await this.sharedCommonService.findMaskRole(adminid);
    let loanList: any = [];

    const companyMap = new Map();

    companyData.forEach((row) => {
      if (!companyMap.has(row.id)) {
        companyMap.set(row.id, row);
      }
    });
    // if (status == '0') {
    //   // Get last approved amount
    //   const totalUserIds = loanData.rows.map((el) => el.userId);
    //   const loanAttr = ['id', 'netApprovedAmount', 'userId'];
    //   const loanOptions = {
    //     where: { loanStatus: 'Complete', userId: totalUserIds },
    //     order: [['id', 'DESC']],
    //   };
    //   loanList = await this.loanRepo.getTableWhereData(loanAttr, loanOptions);
    //   if (loanList === k500Error) throw Error();
    // }

    const length = loanData.rows.length;
    let loanAmountRepeater = 0;
    let loanAmountNew = 0;
    let loanCountRepeater = 0;
    let loanCountNew = 0;
    const preparedList = [];
    for (let index = 0; index < length; index++) {
      try {
        const loan = loanData.rows[index];
        const master = loan?.masterData ?? {};
        const user = loan.registeredUsers ?? {};
        const bank = loan?.bankingData ?? {};
        const cibil = loan?.cibilData ?? {};
        const companyData = companyMap.get(loan.companyId);
        const prediction = loan?.predictionData ?? {};
        let isCibilError = false;
        if (cibil?.status !== '1') isCibilError = true;
        const lastCrm = user?.lastCrm;
        const lStatus = loan?.manualVerification ?? '0';
        const updatedAt = loan?.verifiedDate ?? loan?.updatedAt;
        const lastUpdate = this.typeService.getDateFormatted(updatedAt);
        const ecsBounceCount =
          bank?.otherDetails?.ecsDetails?.ecsBounceCount ?? 0;
        const eligibilityDetails = loan?.eligibilityDetails;

        let bureauOverdue = false;
        const {
          checkExperianManualVerifiedOrNot,
          checkCibilManualVerifiedOrNot,
          checkManualVerifiedOrNot,
        } = eligibilityDetails?.messages || {};

        bureauOverdue =
          (checkExperianManualVerifiedOrNot &&
            checkExperianManualVerifiedOrNot !==
              'No manual verification required') ||
          checkCibilManualVerifiedOrNot === 'Manual check: overdue balance' ||
          checkManualVerifiedOrNot === 'Manual check: overdue balance';

        const preparedData: any = {};
        const ExtraData = { manualReasons: [] };
        if (status == '0') {
          const diffInMinutes = this.typeService.dateDifference(
            new Date(),
            user.stageTime,
            'Minutes',
          );
          preparedData['Difference in minutes'] = diffInMinutes;
          preparedData['Waiting time'] = this.getWaitingTime(diffInMinutes);
          const crmData = lastCrm
            ? {
                'CRM Date': this.dateService.readableDate(lastCrm?.createdAt),
                CRM: lastCrm?.statusName,
                Title: lastCrm?.titleName,
                Remark: lastCrm?.remark,
                Disposition: lastCrm?.dispositionName,
              }
            : {};
          if (crmData?.CRM) ExtraData['Last CRM by'] = crmData;
        }

        ExtraData.manualReasons =
          (prediction?.manualReason ?? {}).insights ?? [];
        // Temporary
        if ((prediction?.manualReason ?? {}).salary == false) {
          ExtraData.manualReasons.push('Inconsistent Salary');
        }
        if ((prediction?.manualReason ?? {}).aadhaarLatLong == false) {
          ExtraData.manualReasons.push('Same Aadhaar Active Loan');
        }
        if ((prediction?.manualReason ?? {}).delayPaymentHistory == false) {
          ExtraData.manualReasons.push('CIBIL Delay Days in last 6 Months');
        }

        const predictionData = prediction?.reason
          ? JSON.parse(prediction.reason)
          : {};
        let exactMatchUserIds = predictionData.exactMatchAddressUsers ?? [];
        if (exactMatchUserIds[0]?.userId) {
          exactMatchUserIds = exactMatchUserIds.map((user) => user.userId);
        }
        let userNames: any = [];
        if (
          predictionData?.exactMatchAddressCount &&
          predictionData.exactMatchAddressCount > 0
        ) {
          userNames = await this.userRepository.getTableWhereData(
            ['fullName'],
            {
              where: {
                id: {
                  [Op.in]: exactMatchUserIds,
                },
              },
            },
          );
          if (!userNames || userNames == k500Error) throw new Error();
        }
        preparedData['Suspicious'] = {
          Aadhaar:
            userNames.length > 0
              ? userNames.map((user) => user?.fullName ?? '-')
              : userNames,

          Gender: bank.genderVerified === 0 ? 'Gender mismatch' : '-',
        };
        preparedData['Assign'] = loan?.assignTo
          ? (await this.sharedCommonService.getAdminData(loan?.assignTo))
              ?.fullName ?? '-'
          : '-';
        preparedData['Loan id'] = loan.id ?? '-';
        preparedData['Name'] = user.fullName ?? '-';
        const phone = this.cryptService.decryptPhone(user?.phone) ?? '-';
        const maskedPhone = maskOptions?.isMaskPhone
          ? this.cryptService.dataMasking('phone', phone)
          : phone;
        preparedData['Mobile number'] = maskedPhone;
        if (status == '0') {
          let lastApprovedAmount = '-';
          let previousLoans = loanList.filter((el) => el.userId == user.id);
          if (previousLoans.length > 0) {
            previousLoans = previousLoans.sort((b, a) => a.id - b.id);
            lastApprovedAmount = (+(
              previousLoans[0]?.netApprovedAmount ?? 0
            )).toFixed();
          }
          preparedData['Last approved amount'] = lastApprovedAmount;
          preparedData['Last CRM by'] = lastCrm?.adminName ?? '-';
        }
        preparedData['Completed loans'] = user?.completedLoans ?? 0;
        preparedData['Employment information'] =
          master?.otherInfo?.employmentInfo === ''
            ? '-'
            : master?.otherInfo?.employmentInfo ?? '-';
        preparedData['Company Name'] = companyData?.companyName ?? '-';
        preparedData['Offered amount'] = loan?.loanAmount ?? '0';
        preparedData['Applied amount'] = loan?.netApprovedAmount ?? '0';
        preparedData['Salary'] = Math.round(
          bank?.adminSalary ?? bank?.salary ?? 0,
        );
        preparedData['Cibil score'] = cibil?.cibilScore ?? '-';
        preparedData['PL score'] = cibil?.plScore ?? '-';
        preparedData['CFLScore'] = prediction?.CFLScore ?? '-';
        preparedData['City'] = user?.city ?? '-';
        if (status != '0')
          preparedData[
            status == '1'
              ? 'Approved by'
              : status == '2'
              ? 'Rejected by'
              : 'Last action by'
          ] =
            (
              await this.sharedCommonService.getAdminData(
                loan?.manualVerificationAcceptId,
              )
            )?.fullName ?? '-';
        preparedData[
          status == '0'
            ? 'Last updated'
            : status == '2' || status == '6'
            ? 'Reject date'
            : 'Verified date'
        ] = lastUpdate;
        if (status == '2') preparedData['Reject reason'] = loan?.remark ?? '-';
        else if (status == '1')
          preparedData['Approved reason'] = loan?.approvedReason ?? '-';
        preparedData['userId'] = user.id ?? '-';
        preparedData['assignId'] = loan?.assignTo ?? '-';
        preparedData['Status'] = lStatus == '3' ? '1' : lStatus;
        preparedData['ExtraData'] = ExtraData;
        preparedData['isCibilError'] = isCibilError;
        preparedData['ECS bounce count'] = ecsBounceCount;
        preparedData['bureauOverdue'] = bureauOverdue;
        /// Pin CRM for [Verification -> Final Verification] (Show pin after user's name)
        preparedData['pinCRM'] = null;
        if (loan?.id == user?.pinCrm?.loanId) {
          preparedData['pinCRM'] = {
            pinAdminName: user?.pinCrm?.adminName ?? '-',
            pinDescription: user?.pinCrm?.remark ?? '-',
            pinCreatedAt: user?.pinCrm?.createdAt ?? '-',
            pinAdminRole: user?.pinCrm?.adminRole ?? '-',
          };
        }
        if (user?.completedLoans > 0) {
          loanAmountRepeater += +(loan?.netApprovedAmount ?? 0);
          loanCountRepeater++;
        } else {
          loanAmountNew += +(loan?.netApprovedAmount ?? 0);
          loanCountNew++;
        }
        preparedList.push(preparedData);
      } catch (error) {}
    }
    if (status == '0')
      return {
        count: preparedList.length,
        rows: preparedList,
        loanAmountRepeater: loanAmountRepeater,
        loanAmountNew: loanAmountNew,
        loanCountRepeater: loanCountRepeater,
        loanCountNew: loanCountNew,
      };
    loanData.rows = preparedList;
    return loanData;
  }

  async loanAccept(adminid) {
    const maskOptions = await this.sharedCommonService.findMaskRole(adminid);
    const bankInclude = {
      model: BankingEntity,
      attributes: ['bank', 'accountNumber'],
    };

    const masterInclude = {
      model: MasterEntity,
      attributes: ['otherInfo'],
    };
    const attributes = [
      'id',
      'completedLoans',
      'fullName',
      'phone',
      'lastCrm',
      'typeOfDevice',
      'stageTime',
      'pinCrm',
      'lastOnlineTime',
    ];
    const loanInclude = {
      model: loanTransaction,
      where: { loanStatus: 'InProcess' },
      attributes: [
        'id',
        'kfsStatus',
        'cseRejectBy',
        'loanAcceptStatus',
        'netApprovedAmount',
        'createdAt',
        'companyId',
      ],
      include: [bankInclude, masterInclude],
    };
    const options: any = {
      useMaster: false,
      include: [loanInclude],
      where: { stage: UserStage.LOAN_ACCEPT },
    };

    const userDetails = await this.userRepository.getTableWhereDataWithCounts(
      attributes,
      options,
    );
    if (userDetails === k500Error) throw new Error();

    const comapnyIds = userDetails?.rows
      .map((row) => row.loanData?.[0]?.companyId)
      .filter((item) => item);

    const companyAttr = ['id', 'companyName'];
    const companyOptions = { where: { id: { [Op.in]: comapnyIds } } };
    const companyData = await this.repoManager.getTableWhereData(
      GoogleCompanyResultEntity,
      companyAttr,
      companyOptions,
    );

    const companyMap = new Map();

    companyData.forEach((row) => {
      if (!companyMap.has(row.id)) {
        companyMap.set(row.id, row);
      }
    });

    const preparedList = [];

    for (let index = 0; index < userDetails.rows.length; index++) {
      const userData = userDetails.rows[index];
      const loanData = userData.loanData?.[0] ?? {};

      ///need to check if loan is rejected due to user in activity reason then no need to list it
      if (GLOBAL_FLOW.IS_CSE_REJECTION_FLOW && loanData?.cseRejectBy) continue;

      const masterData = loanData.masterData ?? {};
      const companyData = companyMap.get(loanData.companyId);
      const bankData = loanData?.bankingData ?? {};
      const lastCrm = userData?.lastCrm;
      const crmData = lastCrm
        ? {
            'CRM Date': this.dateService.readableDate(lastCrm?.createdAt),
            CRM: lastCrm?.statusName,
            Title: lastCrm?.titleName,
            Remark: lastCrm?.remark,
            Disposition: lastCrm?.dispositionName,
          }
        : {};
      const ExtraData = {};
      if (crmData?.CRM) ExtraData['Last CRM by'] = crmData;
      const diffInMinutes = this.typeService.dateDifference(
        new Date(),
        userData.stageTime,
        'Minutes',
      );
      const phone = this.cryptService.decryptPhone(userData?.phone) ?? '-';
      const maskedPhone = maskOptions?.isMaskPhone
        ? this.cryptService.dataMasking('phone', phone)
        : phone;

      const account = bankData?.accountNumber ?? '-';
      const maskedAccount = maskOptions?.isDisbursementAccMask
        ? this.cryptService.dataMasking('bankAccount', account)
        : account;
      const prepareData: any = {
        'Difference in minutes': diffInMinutes,
        'Waiting time': this.getWaitingTime(diffInMinutes),
        isOnline: false,
        'Last Active ago': '',
        userId: userData?.id ?? '-',
        'Loan Id': loanData?.id ?? '-',
        Name: userData?.fullName ?? '-',
        'Mobile number': maskedPhone,
        'Last CRM by': lastCrm?.adminName ?? '-',
        'Completed loans': userData?.completedLoans ?? '0',
        'Employment information':
          masterData?.otherInfo?.employmentInfo === ''
            ? '-'
            : masterData?.otherInfo?.employmentInfo ?? '-',
        'Company Name': companyData?.companyName ?? '-',
        'Loan Amount': loanData?.netApprovedAmount ?? '-',
        'Bank name': bankData?.bank ?? '-',
        'Account no': maskedAccount,
        'Created date':
          this.typeService.jsonToReadableDate(loanData.createdAt?.toJSON()) ??
          '-',
        'Loan offer status':
          loanData?.loanAcceptStatus == -1 ? 'Pending' : 'Accepted',
        'KFS status': loanData?.kfsStatus == -1 ? 'Pending' : 'Accepted',
        'Device type':
          userData?.typeOfDevice == '1'
            ? 'IOS'
            : userData?.typeOfDevice == '0'
            ? 'Android'
            : 'Web',
        ExtraData,
      };
      /// Pin CRM for [Verification -> Loan Accept] (Show pin after user's name)
      prepareData['pinCRM'] = null;
      if (loanData?.id == userData?.pinCrm?.loanId) {
        prepareData['pinCRM'] = {
          pinAdminName: userData?.pinCrm?.adminName ?? '-',
          pinDescription: userData?.pinCrm?.remark ?? '-',
          pinCreatedAt: userData?.pinCrm?.createdAt ?? '-',
          pinAdminRole: userData?.pinCrm?.adminRole ?? '-',
        };
      }

      let lastActiveAgoMinutes: any = Infinity;
      if (userData?.lastOnlineTime) {
        const lastOnlineTime = this.typeService.dateTimeToDate(
          userData?.lastOnlineTime,
        );
        lastActiveAgoMinutes = this.typeService.dateDifference(
          lastOnlineTime,
          new Date(),
          'Minutes',
        );
        prepareData.isOnline = lastActiveAgoMinutes <= 5;
        prepareData['Last Active ago'] =
          this.typeService.formateMinutes(lastActiveAgoMinutes);
      }
      preparedList.push(prepareData);
    }
    return { rows: preparedList, count: preparedList.length };
  }

  async eMandate(adminId) {
    const maskOptions: any = await this.sharedCommonService.findMaskRole(
      adminId,
    );
    // Query preparation
    const subscriptionInclude: { model; attributes? } = {
      model: SubScriptionEntity,
    };
    subscriptionInclude.attributes = [
      'accountNumber',
      'createdAt',
      'mode',
      'response',
      'status',
    ];
    const bank = { model: BankingEntity, attributes: ['mandateBank'] };
    const loanInclude: { model; attributes?; include?; required } = {
      model: loanTransaction,
      required: true,
    };

    loanInclude.attributes = [
      'mandateAttempts',
      'esign_id',
      'netApprovedAmount',
      'subscriptionId',
      'appType',
      'insuranceOptValue',
      'cseRejectBy',
    ];

    loanInclude.include = [subscriptionInclude, bank];
    const masterInclude: { model; attributes?; include? } = {
      model: MasterEntity,
    };
    masterInclude.include = [loanInclude];
    masterInclude.attributes = ['loanId', 'otherInfo'];
    const include = [masterInclude];
    const attributes = [
      'city',
      'completedLoans',
      'fullName',
      'id',
      'lastOnlineTime',
      'phone',
      'stageTime',
      'typeOfDevice',
      'lastCrm',
      'pinCrm',
    ];
    const options = {
      useMaster: false,
      include,
      where: { stage: UserStage.MANDATE },
    };
    // Query
    const userList = await this.userRepository.getTableWhereData(
      attributes,
      options,
    );
    if (userList == k500Error) return kInternalError;

    // Fine tuning
    const preparedList = [];
    let loanAmountRepeater = 0;
    let loanAmountNew = 0;
    let loanCountRepeater = 0;
    let loanCountNew = 0;
    for (let index = 0; index < userList.length; index++) {
      try {
        const userData = userList[index];
        const masterData = userData.masterData ?? {};
        const loanData = masterData.loanData ?? {};

        ///need to check if loan is rejected due to user in activity reason then no need to list it
        if (GLOBAL_FLOW.IS_CSE_REJECTION_FLOW && loanData?.cseRejectBy)
          continue;

        const bankData = loanData?.bankingData ?? {};
        const subscriptionData = loanData.subscriptionData ?? {};
        const lastCrm = userData?.lastCrm;
        const appType = loanData?.appType;
        const crmData = lastCrm
          ? {
              'CRM Date': this.dateService.readableDate(lastCrm?.createdAt),
              CRM: lastCrm?.statusName,
              Title: lastCrm?.titleName,
              Remark: lastCrm?.remark,
              Disposition: lastCrm?.dispositionName,
            }
          : {};
        const ExtraData = {};
        if (crmData?.CRM) ExtraData['Last CRM by'] = crmData;
        const diffInMinutes = this.typeService.dateDifference(
          new Date(),
          userData.stageTime,
          'Minutes',
        );
        const phone = this.cryptService.decryptPhone(userData?.phone) ?? '-';
        const maskedPhone = maskOptions?.isMaskPhone
          ? this.cryptService.dataMasking('phone', phone)
          : phone;

        const account = subscriptionData?.accountNumber ?? '-';
        const maskedAccount = maskOptions?.isDisbursementAccMask
          ? this.cryptService.dataMasking('bankAccount', account)
          : account;
        const preparedData: any = {
          'Difference in minutes': diffInMinutes,
          'Waiting time': this.getWaitingTime(diffInMinutes),
          isOnline: false,
          'Last Active ago': '',
          'Loan Id': masterData?.loanId ?? '-',
          Name: userData?.fullName ?? '-',
          'Mobile number': maskedPhone,
          'Last CRM by': lastCrm?.adminName ?? '-',
          'Completed loans': userData?.completedLoans ?? '0',
          'Employment information':
            masterData?.otherInfo?.employmentInfo === ''
              ? '-'
              : masterData?.otherInfo?.employmentInfo ?? '-',
          'Loan Amount': loanData?.netApprovedAmount ?? '-',
          Platform: subscriptionData?.mode ?? '-',
          'Bank name': bankData?.mandateBank ?? '-',
          'Account number': maskedAccount,
          Attempts: subscriptionData?.mandateAttempts ?? '0',
          'Created date': '-',
          'Device type':
            userData?.typeOfDevice == '1'
              ? 'IOS'
              : userData?.typeOfDevice == '0'
              ? 'Android'
              : 'Web',
          'Insurance status': YES_NO_OBJECT[loanData?.insuranceOptValue] ?? '-',
          'Reject reason': '-',
          Status: subscriptionData?.status?.toUpperCase() ?? kInitiated,
          userId: userData?.id ?? '-',
          mandateId: loanData?.subscriptionId ?? '-',
          ExtraData,
          appType,
        };
        /// Pin CRM for [Verification -> E-Mandate] (Show pin after user's name)
        preparedData['pinCRM'] = null;
        if (masterData?.loanId == userData?.pinCrm?.loanId) {
          preparedData['pinCRM'] = {
            pinAdminName: userData?.pinCrm?.adminName ?? '-',
            pinDescription: userData?.pinCrm?.remark ?? '-',
            pinCreatedAt: userData?.pinCrm?.createdAt ?? '-',
            pinAdminRole: userData?.pinCrm?.adminRole ?? '-',
          };
        }
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = this.typeService.dateTimeToDate(
            userData?.lastOnlineTime,
          );
          lastActiveAgoMinutes = this.typeService.dateDifference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          preparedData.isOnline = lastActiveAgoMinutes <= 5;
          preparedData['Last Active ago'] =
            this.typeService.formateMinutes(lastActiveAgoMinutes);
        }
        if (subscriptionData.createdAt) {
          preparedData['Created date'] = this.typeService.jsonToReadableDate(
            subscriptionData.createdAt.toJSON(),
          );
        }
        if (subscriptionData.response) {
          const responseData = JSON.parse(subscriptionData?.response);
          preparedData['Reject reason'] =
            responseData?.cf_message ??
            responseData?.errorMsg ??
            responseData?.error_description ??
            '';
          if (preparedData['Reject reason']) preparedData.Status = kFailed;
        }

        if (userData?.completedLoans > 0) {
          loanAmountRepeater += +(loanData?.netApprovedAmount ?? 0);
          loanCountRepeater++;
        } else {
          loanAmountNew += +(loanData?.netApprovedAmount ?? 0);
          loanCountNew++;
        }
        preparedList.push(preparedData);
      } catch (error) {}
    }
    return {
      rows: preparedList,
      count: preparedList.length,
      loanAmountRepeater: loanAmountRepeater,
      loanAmountNew: loanAmountNew,
      loanCountRepeater: loanCountRepeater,
      loanCountNew: loanCountNew,
    };
  }

  //#endregion
  async disbursement(adminId) {
    const maskOptions: any = await this.sharedCommonService.findMaskRole(
      adminId,
    );
    // Query preparation
    const bankingInclude: { model; attributes? } = { model: BankingEntity };
    bankingInclude.attributes = ['disbursementBank'];
    const disbursementInclude: { model; attributes? } = {
      model: disbursementEntity,
    };
    disbursementInclude.attributes = [
      'payout_id',
      'source',
      'status',
      'response',
    ];
    const loanInclude: { model; attributes?; include? } = {
      model: loanTransaction,
    };
    loanInclude.attributes = ['netApprovedAmount', 'companyId'];
    loanInclude.include = [bankingInclude, disbursementInclude];

    const masterInclude: { model; attributes?; include? } = {
      model: MasterEntity,
    };
    masterInclude.attributes = ['loanId', 'otherInfo'];
    masterInclude.include = [loanInclude];
    const include = [masterInclude];
    const attributes = [
      'completedLoans',
      'fullName',
      'id',
      'phone',
      'stageTime',
      'lastCrm',
    ];
    const options = {
      useMaster: false,
      include,
      where: { stage: UserStage.DISBURSEMENT, stageStatus: 0 },
    };

    // Query
    const userList = await this.userRepository.getTableWhereData(
      attributes,
      options,
    );
    if (userList == k500Error) return kInternalError;

    const comapnyIds = userList
      .map((row) => row.masterData?.loanData?.companyId)
      .filter((item) => item);

    const companyAttr = ['id', 'companyName'];
    const companyOptions = { where: { id: { [Op.in]: comapnyIds } } };
    const companyData = await this.repoManager.getTableWhereData(
      GoogleCompanyResultEntity,
      companyAttr,
      companyOptions,
    );

    const companyMap = new Map();

    companyData.forEach((row) => {
      if (!companyMap.has(row.id)) {
        companyMap.set(row.id, row);
      }
    });

    // Fine tuning
    const preparedList = [];
    let loanAmountRepeater = 0;
    let loanAmountNew = 0;
    let loanCountRepeater = 0;
    let loanCountNew = 0;
    for (let index = 0; index < userList.length; index++) {
      try {
        const userData = userList[index];
        const masterData = userData.masterData ?? {};
        const loanData = masterData.loanData ?? {};
        const bankingData = loanData.bankingData ?? {};
        const companyData = companyMap.get(loanData.companyId);
        let disbursementData = {
          response: '',
          status: 'INITIALIZED',
          source: '',
          payout_id: '',
        };
        const disbursementList = loanData?.disbursementData ?? [];
        if (disbursementList.length > 0) disbursementData = disbursementList[0];
        let disbursementResponse: any = {};
        if (disbursementData.response)
          disbursementResponse = JSON.parse(disbursementData.response);
        const failureReason =
          disbursementResponse?.failure_reason ??
          disbursementResponse?.status_details?.description ??
          disbursementResponse?.Response ??
          '-';
        const invalidDetailsReason = [
          'Payout failed due to invalid beneficiary account details.',
          'Payout failed as the IFSC code is invalid. Please correct the IFSC code and retry.',
          'IFSC Code is Not Valid. Please check and retry.',
        ];
        let isUniversalIFSC = false;
        if (invalidDetailsReason.includes(failureReason))
          isUniversalIFSC = true;

        const lastCrm = userData?.lastCrm;
        const crmData = lastCrm
          ? {
              'CRM Date': this.dateService.readableDate(lastCrm?.createdAt),
              CRM: lastCrm?.statusName,
              Title: lastCrm?.titleName,
              Remark: lastCrm?.remark,
              Disposition: lastCrm?.dispositionName,
            }
          : {};
        const ExtraData = {};
        if (crmData?.CRM) ExtraData['Last CRM by'] = crmData;
        const diffInMinutes = this.typeService.dateDifference(
          new Date(),
          userData?.stageTime,
          'Minutes',
        );
        const phone = this.cryptService.decryptPhone(userData?.phone) ?? '-';
        const maskedPhone = maskOptions?.isMaskPhone
          ? this.cryptService.dataMasking('phone', phone)
          : phone;

        const preparedData: any = {
          'Difference in minutes': diffInMinutes,
          'Waiting time': this.getWaitingTime(diffInMinutes),
          'Loan Id': masterData.loanId ?? '-',
          Name: userData.fullName ?? '-',
          'Mobile number': maskedPhone,
          'Last CRM by': lastCrm?.adminName ?? '-',
          'Completed loans': userData.completedLoans ?? 0,
          'Employment information':
            masterData?.otherInfo?.employmentInfo === ''
              ? '-'
              : masterData?.otherInfo?.employmentInfo ?? '-',
          'Company Name': companyData?.companyName ?? '-',
          'Loan Amount': loanData.netApprovedAmount ?? 0,
          Bank: bankingData.disbursementBank ?? '-',
          Source: disbursementData?.source ?? '-',
          'Payout Id':
            disbursementResponse?.transId ?? disbursementData?.payout_id ?? '-',
          'Failed reason': failureReason ?? '-',
          isUniversalIFSC,
          Status: disbursementData.status?.toUpperCase() ?? 'INITIALIZED',
          userId: userData.id ?? '-',
          ExtraData,
        };

        if (userData?.completedLoans > 0) {
          loanAmountRepeater += +(loanData.netApprovedAmount ?? 0);
          loanCountRepeater++;
        } else {
          loanAmountNew += +(loanData.netApprovedAmount ?? 0);
          loanCountNew++;
        }
        preparedList.push(preparedData);
      } catch (error) {}
    }
    return {
      rows: preparedList,
      count: preparedList.length,
      loanAmountRepeater,
      loanAmountNew,
      loanCountRepeater,
      loanCountNew,
    };
  }
  //#endregion

  private getWaitingTime(diffInMinutes) {
    const today = new Date();
    const createdAt = new Date();
    createdAt.setMinutes(createdAt.getMinutes() - diffInMinutes);
    const finalizedData = {
      minutes: diffInMinutes,
      createdAt: createdAt.toJSON(),
      endDate: today.toJSON(),
    };
    return finalizedData;
  }

  async eSign(adminId) {
    const maskOptions: any = await this.sharedCommonService.findMaskRole(
      adminId,
    );
    // Query preparation
    const eSignInclude: any = { model: esignEntity };
    eSignInclude.attributes = ['createdAt', 'esign_mode', 'nameMissMatch'];
    const loanInclude: { model; attributes?; include?; required } = {
      model: loanTransaction,
      required: true,
    };
    loanInclude.attributes = [
      'assignTo',
      'esign_id',
      'loanAmount',
      'netApprovedAmount',
      'appType',
      'insuranceOptValue',
      'cseRejectBy',
    ];

    loanInclude.include = [eSignInclude];
    const masterInclude: { model; attributes?; include?; required } = {
      model: MasterEntity,
      required: true,
    };
    masterInclude.include = [loanInclude];
    masterInclude.attributes = ['loanId', 'otherInfo'];
    const include = [masterInclude];
    const attributes = [
      'city',
      'completedLoans',
      'fullName',
      'id',
      'lastOnlineTime',
      'phone',
      'stageTime',
      'totalContact',
      'lastCrm',
      'pinCrm',
    ];
    const options = {
      useMaster: false,
      include,
      where: { stage: UserStage.ESIGN },
    };

    // Query
    const userList = await this.userRepository.getTableWhereData(
      attributes,
      options,
    );
    if (userList == k500Error) return kInternalError;

    // Fine tuning
    const preparedList = [];
    let loanAmountRepeater = 0;
    let loanAmountNew = 0;
    let loanCountRepeater = 0;
    let loanCountNew = 0;
    for (let index = 0; index < userList.length; index++) {
      try {
        const userData = userList[index];
        const masterData = userData.masterData ?? {};
        const loanData = masterData.loanData ?? {};

        ///need to check if loan is rejected due to user in activity reason then no need to list it
        if (GLOBAL_FLOW.IS_CSE_REJECTION_FLOW && loanData?.cseRejectBy)
          continue;

        const eSignData = loanData.eSignData ?? {};
        const response = eSignData?.response
          ? JSON.parse(eSignData.response)
          : {};

        const lastCrm = userData?.lastCrm;
        const crmData = lastCrm
          ? {
              'CRM Date': this.dateService.readableDate(lastCrm?.createdAt),
              CRM: lastCrm?.statusName,
              Title: lastCrm?.titleName,
              Remark: lastCrm?.remark,
              Disposition: lastCrm?.dispositionName,
            }
          : {};
        const ExtraData = {};
        if (crmData?.CRM) ExtraData['Last CRM by'] = crmData;
        const diffInMinutes = this.typeService.dateDifference(
          new Date(),
          userData.stageTime,
          'Minutes',
        );
        const appType = loanData?.appType;
        const phone = this.cryptService.decryptPhone(userData?.phone) ?? '-';
        const maskedPhone = maskOptions?.isMaskPhone
          ? this.cryptService.dataMasking('phone', phone)
          : phone;

        const preparedData: any = {
          'Difference in minutes': diffInMinutes,
          'Waiting time': this.getWaitingTime(diffInMinutes),
          isOnline: false,
          'Last Active ago': '-',
          'Loan Id': masterData?.loanId ?? '-',
          Name: userData?.fullName ?? '-',
          'Mobile number': maskedPhone,
          'Last CRM by': lastCrm?.adminName ?? '-',
          'Completed loans': userData?.completedLoans ?? 0,
          'Employment information':
            masterData?.otherInfo?.employmentInfo === ''
              ? '-'
              : masterData?.otherInfo?.employmentInfo ?? '-',
          'Loan Amount': loanData?.netApprovedAmount,
          Source: eSignData?.esign_mode ?? '-',
          'Insurance status': YES_NO_OBJECT[loanData?.insuranceOptValue] ?? '-',
          'Created date': eSignData?.createdAt
            ? this.typeService.jsonToReadableDate(
                eSignData?.createdAt?.toJSON(),
              )
            : '-',
          'Name mismatch': eSignData?.nameMissMatch == true ? 'Yes' : 'No',
          'Esign name':
            response?.aadhaar_name ??
            response?.result?.signer?.fetched_name ??
            '-',
          userId: userData?.id ?? '-',
          esignId: loanData?.esign_id ?? '-',
          Status: '0',
          ExtraData,
          appType,
        };
        /// Pin CRM for [Verification -> E-Sign] (Show pin after user's name)
        preparedData['pinCRM'] = null;
        if (masterData?.loanId == userData?.pinCrm?.loanId) {
          preparedData['pinCRM'] = {
            pinAdminName: userData?.pinCrm?.adminName ?? '-',
            pinDescription: userData?.pinCrm?.remark ?? '-',
            pinCreatedAt: userData?.pinCrm?.createdAt ?? '-',
            pinAdminRole: userData?.pinCrm?.adminRole ?? '-',
          };
        }
        let lastActiveAgoMinutes: any = Infinity;
        if (userData?.lastOnlineTime) {
          const lastOnlineTime = this.typeService.dateTimeToDate(
            userData?.lastOnlineTime,
          );
          lastActiveAgoMinutes = this.typeService.dateDifference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          preparedData.isOnline = lastActiveAgoMinutes <= 5;
          preparedData['Last Active ago'] =
            this.typeService.formateMinutes(lastActiveAgoMinutes);
        }

        if (userData?.completedLoans > 0) {
          loanAmountRepeater += +(loanData?.netApprovedAmount ?? 0);
          loanCountRepeater++;
        } else {
          loanAmountNew += +(loanData?.netApprovedAmount ?? 0);
          loanCountNew++;
        }

        preparedList.push(preparedData);
      } catch (error) {}
    }
    return {
      rows: preparedList,
      count: preparedList.length,
      loanAmountRepeater: loanAmountRepeater,
      loanAmountNew: loanAmountNew,
      loanCountRepeater: loanCountRepeater,
      loanCountNew: loanCountNew,
    };
  }

  // clon migrate api for move Stuck User In Final Bucket
  async migrateUserInFB() {
    try {
      const attributes = ['id', 'status'];
      const options = {
        where: {
          status: {
            bank: { [Op.in]: [1, 3] },
            pan: { [Op.in]: [1, 3] },
            selfie: { [Op.in]: [1, 3] },
            eMandate: -1,
            loan: 4,
          },
        },
      };
      const loanList = await this.masterRepo.getTableWhereData(
        attributes,
        options,
      );
      if (loanList === k500Error) return kInternalError;
      for (let index = 0; index < loanList.length; index++) {
        try {
          const ele = loanList[index];
          const id = ele.id;
          const status = ele.status;
          status.loan = 0;
          status.eligibility = 0;
          const update = { status };
          await this.masterRepo.updateRowData(update, id);
        } catch (error) {}
      }
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // get user cibil score data
  async getCibilScoreData(reqData) {
    try {
      const cibilData = await this.cibilService.funGetUserCibilScoreData(
        reqData,
      );
      return cibilData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }
  //#endregion

  //cibil hit due to cibil error
  async funCibilHitInBanking(body) {
    try {
      const userId = body?.userId;
      const loanId = body?.loanId;
      const cibilForceFetch = body?.cibilForceFetch;
      const attributes = ['id'];
      const options: any = {
        where: { userId },
      };
      const loanData = await this.loanRepo.getRowWhereData(attributes, options);
      if (loanData == k500Error) return kInternalError;
      if (!loanData) return kNoDataFound;

      const cibilResult = await this.cibilService.cibilPersonalLoanScore({
        userId,
        loanId,
        cibilForceFetch,
      });

      if (cibilResult?.message)
        return k422ErrorMessage(
          'Unable to Fetch CIBIL details, Contact IT Support',
        );
      if (cibilResult?.controlData?.success == false)
        return k422ErrorMessage(
          'Unable to Fetch CIBIL details, Contact IT Support',
        );

      if (cibilResult.isLoanDeclined == true) {
        const remark = BAD_CIBIL_SCORE_MSG;
        const adminId = SYSTEM_ADMIN_ID;
        let targetDate = new Date();
        targetDate.setDate(
          targetDate.getDate() + GLOBAL_RANGES.COOL_OFF_WITH_BAD_CIBIL_SCORE,
        );

        ///Reject loan at this step

        await this.adminService.changeBlacklistUser({
          userId,
          adminId,
          type: '2',
          reason: cibilResult?.reason ?? BAD_CIBIL_SCORE_MSG,
          reasonId: cibilResult?.reasonId ?? BAD_CIBIL_SCORE_REASON_ID,
          status: '0',
          nextApplyDate: targetDate,
        });
        const errorMsg = k422ErrorMessage(
          'Loan declined as profile not matched with CIBIL criteria',
        );
        return {
          isLoanDeclined: true,
          ...errorMsg,
        };
      } else if (cibilResult.isLoanDeclined == false) {
        let scoreData: any = {};
        scoreData.cibilScore =
          cibilResult?.responsedata?.consumerCreditData[0]?.scores[0]?.score.replace(
            /^0+/,
            '',
          );
        scoreData.plScore =
          cibilResult?.responsedata?.consumerCreditData[0]?.scores[1]?.score.replace(
            /^0+/,
            '',
          );
        return scoreData;
      }
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  // Edit Company Name Mismatch
  async editCompanyNameMismatch(reqData) {
    try {
      const updatedCompanyName = reqData?.updatedCompanyName
        ?.trim()
        ?.toUpperCase();
      if (!updatedCompanyName) return kParamMissing('updatedCompanyName');

      const adminId = reqData?.adminId;
      if (!adminId) return kParamMissing('adminId');

      const empId = reqData?.empId;
      if (!empId) return kParamMissing('empId');

      // Existing data
      const masterInclude: any = {
        model: MasterEntity,
        attributes: ['id', 'status', 'loanId'],
      };
      const empOptions = { where: { id: empId }, include: [masterInclude] };
      const existingData = await this.employementRepo.getRowWhereData(
        null,
        empOptions,
      );
      if (!existingData || existingData == k500Error) return kInternalError;

      if (existingData?.companyName == updatedCompanyName) return kWrongDetails;

      const status = existingData?.masterData?.status ?? {};
      const masterId = existingData?.masterData?.id;
      const userId = existingData?.userId;
      const loanId = existingData?.masterData?.loanId;

      const extraCompanyDetails = await this.fetchNewCompanyDetails(
        updatedCompanyName,
      );
      if (!extraCompanyDetails) return kInternalError;

      const companyName = extraCompanyDetails.companyName
        ?.trim()
        ?.toUpperCase();
      const companyAddress = extraCompanyDetails.companyAddress;
      const companyPhone = extraCompanyDetails.companyPhone;
      const companyUrl = extraCompanyDetails.companyUrl;

      let companyVerification: any =
        await this.employmentSharedService.verifyAndStoreInfo(
          updatedCompanyName,
          userId,
        );

      if (companyVerification?.message || companyVerification == k500Error)
        return k422ErrorMessage(kErrorMsgs.SERVICE_UNAVAILABLE);
      if (companyVerification?.message) {
        // true for INSTAFINANCE --> PROTEAN --> INSTAFINANCE
        companyVerification = true;
      }
      if (companyVerification == false) {
        status.company = 2;
        status.loan = 2;
        status.eligibility = 2;
      } else status.company = 1;

      const updatedData = {
        companyName,
        companyAddress,
        companyPhone,
        companyUrl,
        updatedCompanyName,
        companyNameChangeBy: adminId,
        companyVerification: status.company,
      };
      // Update the new company details
      await this.employementRepo.updateRowData(updatedData, empId);
      await this.masterRepo.updateRowWhereData(
        { status },
        { where: { id: masterId } },
      );

      // Store old company as history
      existingData.id = undefined;
      const createdData = await this.empHistoryRepo.createRowData(existingData);
      if (createdData == k500Error) return kInternalError;

      const companyData = await this.companyRepo.getRowWhereData(
        ['id', 'companyName'],
        { where: { companyName: updatedCompanyName } },
      );
      if (companyData == k500Error) return kInternalError;

      await this.loanRepo.updateRowWhereData(
        { companyId: companyData?.id },
        { where: { id: loanId } },
      );

      const data = {
        userId: userId,
        type: 'Verification',
        subType: 'Company Name',
        oldData: existingData.companyName ?? '',
        newData: updatedCompanyName,
        adminId,
        ip: reqData?.ip ?? '',
      };
      await this.changeLogsRepo.create(data);
      await this.sharedEligibility.isEligibleCompany({
        userId,
        companyName: updatedCompanyName,
      });
      await this.userServiceV4.routeDetails({ id: userId });
      return true;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private async fetchNewCompanyDetails(companyName: string) {
    const queryData = { needDetails: true, searchText: companyName };
    const companyData = await this.googleService.searchOrganisation(queryData);
    // Not handling errors because user should not get affected for this error
    if (companyData && !companyData.message) {
      const tempData: any = {};
      tempData.companyAddress = companyData.formatted_address ?? '';
      tempData.companyUrl = companyData.website ?? '';
      tempData.companyPhone = companyData.international_phone_number ?? '';
      tempData.companyName = companyData?.name
        ? companyData?.name.toUpperCase()
        : companyName;
      return tempData;
    }
    return null;
  }
  //#endregion

  async manualAssignVerification(reqData) {
    try {
      let assignIds = reqData?.assignId;
      const loanIds = reqData?.loanId;
      const step = reqData?.step;
      if (!assignIds) return kParamMissing('assignId');
      if (!loanIds) return kParamMissing('loanId');
      if (!Object.keys(kVerificationAccessStatus)?.includes(step))
        return kInvalidParamValue('step');
      if (assignIds?.length == 0)
        return k422ErrorMessage('plz provide at least one assignee');
      if (loanIds?.length == 0)
        return k422ErrorMessage('plz provide at least one loanId');

      const allCaData = await this.caAssignmentService.getCAData();
      if (allCaData?.message) return allCaData;
      const validAccessWiseCaData = allCaData.filter(
        (ele) =>
          assignIds.includes(ele?.id) && ele?.verificationAccessStatus?.[step],
      );
      assignIds = validAccessWiseCaData.map((ele) => ele.id);
      if (assignIds?.length == 0)
        return k422ErrorMessage('plz provide at least one valid assignee');
      for (let i = 0; i < loanIds.length; i++) {
        const loanId = loanIds[i];
        await this.caAssignmentService.assignCA(
          kVerificationAccessStatus[step],
          loanId,
          assignIds,
        );
      }
      return kSuccessMessage(kAssignmentSuccessMessage);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  private getEligibleAdmin(adminIds, adminCounts, excludedAdmin = null) {
    let eligibleAdmins = null;
    if (excludedAdmin) {
      eligibleAdmins = adminIds
        ?.filter((adminId) => adminId != excludedAdmin)
        ?.sort((a, b) => adminCounts[a] - adminCounts[b]);
    } else {
      eligibleAdmins = adminIds?.sort(
        (a, b) => adminCounts[a] - adminCounts[b],
      );
    }
    return eligibleAdmins?.[0];
  }

  private async shuffleAssigns(loanIds, assignIds, forFinal) {
    const adminCounts = Object.fromEntries(assignIds.map((id) => [id, 0]));
    let loanToBankAdmin = {};
    const newLoanToAdminMap: any = {};
    if (forFinal) {
      const bankData = await this.bankingRepo.getTableWhereData(
        ['adminId', 'loanId'],
        {
          where: { loanId: loanIds },
        },
      );
      if (bankData == k500Error) return kInternalError;

      loanToBankAdmin = Object.fromEntries(
        bankData.map((item) => [item.loanId, item.adminId]),
      );
    }

    loanIds.forEach((id) => {
      const excludedAdmin = loanToBankAdmin[id];
      const newAdmin = this.getEligibleAdmin(
        assignIds,
        adminCounts,
        excludedAdmin,
      );

      if (newAdmin) {
        newLoanToAdminMap[id] = newAdmin;
        adminCounts[newAdmin]++;
      }
    });

    let newAdminToLoanMapObj: any = {};
    for (const loanId in newLoanToAdminMap) {
      const adminId = newLoanToAdminMap[loanId];
      if (newAdminToLoanMapObj[adminId])
        newAdminToLoanMapObj[adminId].push(loanId);
      else newAdminToLoanMapObj[adminId] = [loanId];
    }
    const result: any = [];
    for (const admin in newAdminToLoanMapObj) {
      result.push({ assignee: admin, loans: newAdminToLoanMapObj[admin] });
    }
    return result;
  }

  async manualAssignUserStuck(reqData) {
    try {
      const assignIds = reqData?.assignId;
      const userIds = reqData?.userId;
      if (!assignIds) return kParamMissing('assignId');
      if (!userIds) return kParamMissing('userId');

      const result = [];
      const totalUsers = userIds.length;
      const totalAssignees = assignIds.length;
      const userPerAssignee = Math.floor(totalUsers / totalAssignees);
      const remainingUsers = totalUsers % totalAssignees;

      const attr = ['masterId', 'id'];
      const options = {
        where: {
          id: { [Op.in]: userIds },
        },
      };
      const masterDetails = await this.userRepository.getTableWhereData(
        attr,
        options,
      );
      if (masterDetails == k500Error) return kInternalError;

      const userMasterDetails = masterDetails.reduce((acc, master) => {
        acc[master.id] = master.masterId;
        return acc;
      }, {});

      // sort assignId in descending order
      const sortedAssignId = assignIds.sort((a, b) => b - a);

      let userIndex = 0;

      // distribute loanIds to all assignees in equal part
      sortedAssignId.forEach((assignee, index) => {
        const userToAssign = userPerAssignee + (index < remainingUsers ? 1 : 0);
        const assignedUsers = userIds.slice(
          userIndex,
          userIndex + userToAssign,
        );

        const usersWithMasterIds = assignedUsers.map(
          (userId) => userMasterDetails[userId],
        );
        result.push({
          assignee,
          users: assignedUsers,
          masterId: usersWithMasterIds,
        });
        userIndex += userToAssign;
      });

      const query = result
        .filter((entry) => entry.masterId.length > 0)
        .map(
          (entry) =>
            `UPDATE public."MasterEntities" SET "assignedCSE" = ${entry.assignee} WHERE "id" IN (${entry.masterId});`,
        )
        .join('');
      const queryData = await this.repoManager.injectRawQuery(
        MasterEntity,
        query,
      );
      if (queryData == k500Error) return k500Error;
      return kSuccessMessage(kAssignmentSuccessMessage);
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async cseAdmins() {
    let allAdminData = await this.redisService.get(REDIS_KEY.ADMINS_DATA);
    if (!allAdminData) {
      await this.adminRedisSyncService.storeAllAdminsOnRedis();
      allAdminData = await this.redisService.get(REDIS_KEY.ADMINS_DATA);
    }
    if (allAdminData) allAdminData = JSON.parse(allAdminData);
    else throw new Error();

    const cseAdmins = [];
    for (const admin of allAdminData) {
      if (admin?.roleId == CSE_ROLE_ID && admin?.isActive == '1') {
        cseAdmins.push({
          id: admin?.id ?? '-',
          fullName: admin?.fullName ?? '-',
          isActive: admin?.isActive ?? '-',
        });
      }
    }
    return cseAdmins;
  }

  async shiftWiseUserStuckAssignment() {
    try {
      const activeShiftDetails =
        await this.assignmentService.fetchEmployeesInShift(CSE_ROLE_ID);
      if (activeShiftDetails?.employeesData?.length === 0) return;
    } catch (error) {}
    const maxMasterId = await this.redisService.get('MAX_MASTER_ID');
    const attributes = ['id'];
    const option: any = {
      where: {
        assignedCSE: 37,
      },
    };

    if (maxMasterId) {
      option.where.id = {
        [Op.gt]: maxMasterId,
      };
    }
    let userDetails = await this.masterRepo.getTableWhereData(
      attributes,
      option,
    );

    if (userDetails == k500Error) throw new Error();
    // Extract IDs and find the greatest ID using a loop
    let masterIds = [];
    let greatestId = userDetails[0]?.id;
    for (let user of userDetails) {
      masterIds.push(user.id);
      if (user.id > greatestId) {
        greatestId = user.id;
      }
    }
    if (greatestId) await this.redisService.set('MAX_MASTER_ID', greatestId);
    const result = await this.sharedEligibility.assignToCSE(masterIds);
    return result;
  }

  async prepareData(data) {
    try {
      let finalData: any = [];
      const uniqueStageArr = ['E-Sign Completed', 'Loan Disbursement'];
      for (let i = 0; i < data.length; i++) {
        const item = data[i];
        if (uniqueStageArr.includes(item.stage)) {
          const idx = finalData.findIndex((el) => el['Stage'] == item.stage);
          if (idx != -1) finalData.splice(idx, 1);
        }
        let prepareData: any = {};
        prepareData['Id'] = item?.id;
        prepareData['LoanId'] = item?.loanId ?? '-';
        prepareData['UserId'] = item?.userId ?? '-';
        prepareData['Stage'] = item?.stage ?? '-';
        prepareData['CreatedAt'] = item?.createdAt ?? '-';
        prepareData['Ip'] = item?.ip ?? '-';
        prepareData['Device Id'] = item?.deviceId ?? '-';
        prepareData['City'] = item?.city ?? '-';
        prepareData['Ip Location'] = item?.ipLocation ?? '-';
        prepareData['Ip Country'] = item.ipCountry ?? '-';
        prepareData['UTR'] = item?.otherDetails?.utr ?? '-';
        prepareData['Loan Amount'] = item?.otherDetails?.loanAmount ?? '-';
        prepareData['Bank Account'] = item?.otherDetails?.bankAccount ?? '-';
        prepareData['Payout Account'] =
          item?.otherDetails?.payoutAccount ?? '-';
        prepareData['Mode Of Payment'] =
          item?.otherDetails?.modeOfPayment ?? '-';
        prepareData['Device'] = item?.otherDetails?.device ?? '-';
        prepareData['Repay Account'] = item?.otherDetails?.repayAccount ?? '-';
        prepareData['Account Deducted'] =
          item?.otherDetails?.accountDeductes ?? '-';

        finalData.push(prepareData);
      }
      return finalData;
    } catch (error) {
      this.errorContextService.throwAndSetCtxErr(error);
      return kInternalError;
    }
  }

  async resetActiveShiftEmployees(isRefresh = false) {
    if (!isRefresh) {
      const shiftDetails = await this.redisService.get(
        `SHIFTPLAN_${CSE_ROLE_ID}`,
      );
      if (shiftDetails?.length) return;
    }
    const attributes = [
      'roleId',
      'startTime',
      'endTime',
      'employees',
      'updatedBy',
      'updatedAt',
      'id',
      'isActive',
    ];
    const shiftDetails = await this.repoManager.getTableWhereData(
      shiftPlanEntity,
      attributes,
      { where: { roleId: CSE_ROLE_ID, isActive: '1' } },
    );
    if (shiftDetails == k500Error) throw new Error();
    shiftDetails.forEach((shift) => {
      shift.employees = shift.employees.map((employeeId) => ({
        id: employeeId,
        caseCount: 0,
      }));
    });
    await this.redisService.set(
      `SHIFTPLAN_${CSE_ROLE_ID}`,
      JSON.stringify(shiftDetails),
    );
  }

  async getOnHoldUserData(query) {
    const adminid = query?.adminid;
    const result = await this.getOnHoldData(query);
    return await this.prepareOnHoldData(result, adminid);
  }

  private async getOnHoldData(query) {
    let { status, adminId, loanWhere, stage } = await this.prepareOnHoldFilters(
      query,
    );

    let finalData: any = {};

    // Get User data
    const userData = await this.repoManager.getTableWhereData(
      registeredUsers,
      ['id', 'lastCrm', 'pinCrm', 'lastOnlineTime', 'masterId'],
      { where: { stage: UserStage.ON_HOLD } },
    );
    if (userData == k500Error) throw new Error();

    // Get master data
    const masterIds = userData?.map((el) => el.masterId);
    const masterData = await this.masterRepo.getTableWhereData(
      ['assignedCSE', 'userId'],
      { where: { id: masterIds }, order: [['id', 'DESC']] },
    );
    if (masterData == k500Error) throw new Error();

    // map userIds
    const userIds = userData?.map((el) => el.id);

    // Get emp data
    const empData = await this.repoManager.getTableWhereData(
      employmentDetails,
      ['companyName', 'userId', 'salaryDate', 'salary'],
      { where: { userId: userIds }, order: [['id', 'DESC']] },
    );
    if (empData == k500Error) throw new Error();

    finalData.empData = empData;

    // Get bank data
    const bankWhere: any = {
      userId: userIds,
    };
    if (adminId) bankWhere.assignedTo = adminId;
    if (stage == '1') {
      bankWhere.salaryVerification = status;
    }
    const bankAttr = [
      'id',
      'loanId',
      'additionalURLsDates',
      'additionalURLs',
      'isSuspicious',
      'salary',
      'salaryDate',
      'bankStatement',
      'stmtStartDate',
      'stmtEndDate',
      'bank',
      'nameMissMatch',
      'name',
      'nameSimilarity',
    ];
    const bankOptions = {
      where: bankWhere,
      useMaster: false,
      order: [['id', 'DESC']],
    };

    const bankData = await this.repoManager.getTableWhereData(
      BankingEntity,
      bankAttr,
      bankOptions,
    );
    if (bankData === k500Error) throw new Error();

    finalData.bankData = bankData;

    const bankIds = [...new Set(bankData.map((el) => el.id))];
    // Prepare loanWhere based on stage
    let finalLoanWhere: any = {
      ...loanWhere,
      loanStatus: ['InProcess', 'Accepted'],
    };

    if (stage == '1') {
      finalLoanWhere.bankingId = bankIds;
    } else if (stage == '3') {
      finalLoanWhere.manualVerification = status;
    } else {
      finalLoanWhere[Op.or] = [
        { bankingId: bankIds },
        { manualVerification: status },
      ];
    }

    // Get loan data
    const loanAttr = [
      'id',
      'fullName',
      'phone',
      'hold_reason',
      'hold_stage',
      'hold_timestamp',
      'hold_by',
      'assignTo',
      'userId',
      'hold_other_details',
      'cibilId',
    ];
    const loanOptions: any = {
      where: finalLoanWhere,
      useMaster: false,
      order: [['id', 'DESC']],
    };

    const loanData = await this.repoManager.getTableCountWhereData(
      loanTransaction,
      loanAttr,
      loanOptions,
    );
    if (loanData === k500Error) throw new Error();

    const cibilIds = [...new Set(loanData.rows.map((el) => el.cibilId))];

    const cibilAttr = ['id', 'status', 'loanId'];
    const cibilOptions = {
      where: { id: cibilIds },
      order: [['id', 'DESC']],
    };
    const cibilData = await this.repoManager.getTableWhereData(
      CibilScoreEntity,
      cibilAttr,
      cibilOptions,
    );
    if (cibilData === k500Error) throw new Error();
    finalData.cibilData = cibilData;

    finalData.loanData = loanData;
    finalData.userData = userData;
    finalData.masterData = masterData;
    return finalData;
  }

  private async prepareOnHoldFilters(query) {
    // Stage 1-Bank Statement, Stage 2- Final Verification
    const stage = query?.onHoldStage ?? 'ALL';
    let status = query.status;
    let searchText = query?.searchText;
    const adminId = query?.adminId;
    let loanWhere: any = {};

    if (searchText) {
      const searchData: any = await this.commonService.getSearchData(
        searchText,
      );
      if (searchData?.text != '' && searchData?.type == 'Name') {
        loanWhere = { fullName: { [Op.iRegexp]: searchData.text } };
      } // Search by user's phone number
      else if (searchData?.text != '' && searchData?.type == 'Number') {
        loanWhere = { phone: { [Op.like]: searchData.text } };
      } else if (searchData?.text != '' && searchData?.type == 'LoanId') {
        loanWhere.id = searchData.text;
      }
    }

    return { status, adminId, loanWhere, stage };
  }

  private async prepareOnHoldData(result, adminid) {
    const maskOptions = await this.sharedCommonService.findMaskRole(adminid);
    const loanData = result?.loanData;
    const bankData = result?.bankData;
    const empData = result?.empData;
    const cibilData = result?.cibilData;
    const userData = result?.userData;
    const masterData = result?.masterData;

    const length = loanData.rows.length;
    let finalData = [];
    for (let index = 0; index < length; index++) {
      try {
        const loan = loanData.rows[index];
        const filteredBankData = Array.isArray(bankData)
          ? bankData.filter((el) => el.loanId == loan.id)
          : [];
        const filteredEmpData = Array.isArray(empData)
          ? empData.filter((el) => el.userId == loan.userId)
          : [];
        const filteredCibilData = Array.isArray(cibilData)
          ? cibilData.filter((el) => el.loanId == loan.id)
          : [];
        const filteredUserData = Array.isArray(userData)
          ? userData.find((el) => el.id == loan.userId)
          : {};
        const filteredMasterData = Array.isArray(masterData)
          ? masterData?.find((el) => el?.userId == loan?.userId)
          : {};
        const assign = await this.sharedCommonService.getAdminData(
          loan?.assignTo,
        );
        const holdBy = await this.sharedCommonService.getAdminData(
          loan?.hold_by,
        );
        const assignCSE = await this.sharedCommonService.getAdminData(
          filteredMasterData?.assignedCSE,
        );
        const phone = await this.cryptService.decryptPhone(loan?.phone);
        const diffInMinutes = this.typeService.dateDifference(
          new Date(),
          loan.hold_timestamp,
          'Minutes',
        );
        const holdDate = this.dateService.readableDate(loan?.hold_timestamp);
        const finalAdditionalURLs =
          this.bankingService.manageNGetAdditionalUrls(
            filteredBankData[0]?.additionalURLsDates,
            filteredBankData[0]?.additionalURLs,
          );
        const bankStmtObj: any = {
          url: filteredBankData[0]?.bankStatement ?? '-',
          stmtStartDate: filteredBankData[0]?.stmtStartDate
            ? this.typeService.getDateFormated(
                filteredBankData[0].stmtStartDate,
                '/',
              )
            : '-',
          stmtEndDate: filteredBankData[0]?.stmtEndDate
            ? this.typeService.getDateFormated(
                filteredBankData[0].stmtEndDate,
                '/',
              )
            : '-',
          bankCode: filteredBankData[0]?.bank ?? '-',
        };
        let isCibilError = false;
        if (
          filteredCibilData[0]?.status == '2' ||
          filteredCibilData[0]?.status == '3'
        )
          isCibilError = true;
        const lastCrm = filteredUserData?.lastCrm;
        const crmData = lastCrm
          ? {
              'CRM Date': this.dateService.readableDate(lastCrm?.createdAt),
              CRM: lastCrm?.statusName,
              Title: lastCrm?.titleName,
              Remark: lastCrm?.remark,
              Disposition: lastCrm?.dispositionName,
            }
          : {};

        const obj: any = {};
        obj['Difference in minutes'] = diffInMinutes;
        obj['Waiting time'] = this.getWaitingTime(diffInMinutes);
        obj['Assigned CSE'] = assignCSE?.fullName ?? '-';
        obj['Loan id'] = loan.id;
        obj.userId = loan.userId;
        obj.Name = loan?.fullName ?? '-';
        const maskedPhone = maskOptions?.isMaskPhone
          ? this.cryptService.dataMasking('phone', phone)
          : phone;

        obj['Mobile number'] = maskedPhone ?? '-';
        obj['Hold reason'] = loan?.hold_reason ?? '-';
        obj['Stage'] =
          loan?.hold_stage == 1 || loan?.hold_stage == 2
            ? 'Bank Statements'
            : 'Final Verification';
        obj['Assign'] = assign?.fullName ?? '-';
        obj['assignId'] = loan?.assignTo ?? '-';
        obj['Hold by'] = holdBy?.fullName ?? '-';
        obj['Hold date'] = holdDate ?? '-';
        obj['Additional Urls'] = finalAdditionalURLs;
        obj['Company name'] = filteredEmpData[0]?.companyName ?? '-';
        obj['userSalaryDate'] = filteredEmpData[0]?.salaryDate ?? '-';
        obj['Salary'] = filteredEmpData[0].salary ?? '-';
        obj['isSuspicious'] =
          filteredBankData[0]?.isSuspicious == 1 ? true : false;
        obj['actualSalary'] = filteredBankData[0]?.salary ?? '-';
        obj['systemDate'] = filteredBankData[0]?.salaryDate ?? '-';
        obj['Statement'] = filteredBankData[0]?.bankStatement
          ? bankStmtObj
          : '-';
        obj['Applied bank'] = filteredBankData[0]?.bank ?? '-';
        obj['nameMissMatch'] =
          filteredBankData[0]?.nameMissMatch == 0 ? true : '-';
        obj['accountName'] = filteredBankData[0]?.name ?? '-';
        obj['nameSimilarity'] = filteredBankData[0]?.nameSimilarity ?? '-';
        obj['onHoldDocuments'] = ON_HOLD_DOCUMENT_REQUIREMENTS[
          loan?.hold_reason
        ]
          ? true
          : false;
        obj['documentsUploaded'] =
          loan?.hold_stage == 2 || loan?.hold_stage == 4 ? true : false;
        obj.isCibilError = isCibilError;
        obj['Last CRM by'] = lastCrm?.adminName ?? '-';
        if (crmData && crmData.CRM) {
          if (!obj.ExtraData) obj.ExtraData = {};
          obj.ExtraData['Last CRM by'] = crmData;
        }
        let lastActiveAgoMinutes: any = Infinity;
        if (filteredUserData?.lastOnlineTime) {
          const lastOnlineTime = this.typeService.dateTimeToDate(
            filteredUserData?.lastOnlineTime,
          );
          lastActiveAgoMinutes = this.typeService.dateDifference(
            lastOnlineTime,
            new Date(),
            'Minutes',
          );
          obj['isOnline'] = lastActiveAgoMinutes <= 5;
          obj['Last Active ago'] =
            this.typeService.formateMinutes(lastActiveAgoMinutes);
        }
        obj['pinCRM'] = null;
        if (
          filteredBankData?.length &&
          filteredBankData?.[0]?.loanId == filteredUserData?.pinCrm?.loanId
        ) {
          obj['pinCRM'] = {
            pinAdminName: filteredUserData?.pinCrm?.adminName ?? '-',
            pinDescription: filteredUserData?.pinCrm?.remark ?? '-',
            pinCreatedAt: filteredUserData?.pinCrm?.createdAt ?? '-',
            pinAdminRole: filteredUserData?.pinCrm?.adminRole ?? '-',
          };
        }
        obj['onHoldStage'] =
          loan?.hold_stage == 1 || loan?.hold_stage == 2 ? 1 : 3;
        finalData.push(obj);
      } catch (error) {}
    }
    return { count: finalData.length, rows: finalData };
  }

  async onHoldUserDocuments(body) {
    const loanId = body?.loanId;
    if (!loanId) return kParamMissing('loanId');
    const holdReason = body?.holdReason;
    if (!holdReason) return kParamMissing('holdReason');

    // Get loan data
    const loanAttr = ['hold_other_details'];
    const loanOptions: any = {
      where: { id: loanId },
      useMaster: false,
    };

    const loanData = await this.repoManager.getRowWhereData(
      loanTransaction,
      loanAttr,
      loanOptions,
    );
    if (loanData === k500Error) throw new Error();

    const holdDetails = ON_HOLD_DOCUMENT_REQUIREMENTS[holdReason];
    if (!holdDetails) return {};

    const user_hold_details = JSON.parse(loanData?.hold_other_details);
    let result;
    if (holdDetails?.includesTransaction) {
      result = await Promise.all(
        Object.entries(user_hold_details).map(
          async ([key, value]: [string, any]) => {
            const adminData = value?.adminId
              ? await this.sharedCommonService.getAdminData(value.adminId)
              : null;

            return {
              Narration: key,
              Date: value?.date,
              Amount: value?.Amount,
              'Document Uploaded': holdDetails?.adminText ?? '-',
              'Uploaded By':
                adminData?.fullName == 'System'
                  ? 'Customer'
                  : adminData?.fullName,
              View: value?.doc ?? '-',
              key: key,
            };
          },
        ),
      );
    } else {
      const docKeys: string[] = [
        ...holdDetails?.required,
        ...holdDetails?.optional,
        ...holdDetails?.atLeastOne.flat(), // Flatten nested arrays
      ];
      result = await Promise.all(
        docKeys.map(async (key) => {
          const value = user_hold_details ? user_hold_details[key] : null;
          const adminData = value?.adminId
            ? await this.sharedCommonService.getAdminData(value.adminId)
            : '-';

          return {
            'Document Uploaded': key,
            'Uploaded By':
              adminData?.fullName == 'System'
                ? 'Customer'
                : adminData?.fullName,
            View: value?.doc ?? '-',
            key: key,
          };
        }),
      );
    }
    return result;
  }
}
