// Imports
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { AdminService } from 'src/admin/admin/admin.service';
import {
  COMPANY_INCORPORATION_MIN_DAY,
  MIN_DAYS_FOR_NEXT_EPFO_UPDATE,
  SYSTEM_ADMIN_ID,
  validAppTypeValues,
} from 'src/constants/globals';
import { k500Error } from 'src/constants/misc';
import {
  nInstafinaceLlpComapny,
  nInstafinaceLlpDirectorName,
  nInstafinacePrivateComapny,
  nInstafinacePrivateDirectorName,
} from 'src/constants/network';
import {
  kInternalError,
  kInvalidParamValue,
  kParamMissing,
} from 'src/constants/responses';
import {
  COMPANY_STATUS_IS_NOT_ACTIVE,
  INCORPORATE_DATE_IS_LESS_THAN_ONE_YEAR,
} from 'src/constants/strings';
import { EPFODetailsEntity } from 'src/entities/EPFODetails';
import { HashPhoneEntity } from 'src/entities/hashPhone.entity';
import { BlackListCompanyRepository } from 'src/repositories/blacklistCompanies.repository';
import { CompanyRepository } from 'src/repositories/google.company.repository';
import { RepositoryManager } from 'src/repositories/repository.manager';
import { BefiscService } from 'src/thirdParty/befisc/befisc.service';
import { InstaFinancialService } from 'src/thirdParty/instafinancial/instafinancial.service';
import { ProteanService } from 'src/thirdParty/protean/protean.servics';
import { SlackService } from 'src/thirdParty/slack/slack.service';
import { APIService } from 'src/utils/api.service';
import { CryptService } from 'src/utils/crypt.service';
import { TypeService } from 'src/utils/type.service';

@Injectable()
export class EmploymentSharedService {
  constructor(
    private readonly instaFinancial: InstaFinancialService,
    private readonly typeService: TypeService,
    @Inject(forwardRef(() => AdminService))
    private readonly adminService: AdminService,
    private readonly api: APIService,
    private readonly cryptService: CryptService,
    @Inject(forwardRef(() => ProteanService))
    private readonly proteanService: ProteanService,
    @Inject(forwardRef(() => BefiscService))
    private readonly befiscService: BefiscService,
    private readonly slackService: SlackService,
    private readonly blackListedCompanyRepo: BlackListCompanyRepository,
    private readonly CompanyRepo: CompanyRepository,
    private readonly repoManager: RepositoryManager,
  ) {}

  async verifyAndStoreInfo(companyName, userId) {
    const findCinNumber: any =
      await this.instaFinancial.searchCompanyInInstafinanc(companyName);
    if (!findCinNumber?.length || findCinNumber?.message) return kInternalError;
    let finalJsonData: any;
    let cin: any;
    let id: any;
    if (!findCinNumber.length) return kInternalError;
    else {
      const find = findCinNumber.find((f) => f.id != null);
      const company_data = find ?? findCinNumber[0];

      let companyDetails = company_data.companyDetails;
      if (companyDetails) finalJsonData = company_data;
      //for run instafinlease only
      else {
        //get data from Insta-Finance API
        companyName =
          company_data?.name?.replace(/[^a-zA-Z0-9]+/g, '-') ??
          company_data.companyName;
        cin = company_data?.cin ?? company_data?.CIN;
        id = company_data.id;
        let html1;
        let html2;
        let url;
        let url2;
        let finalData;
        let finalData2;
        if (
          companyName.includes('LLP') ||
          companyName.includes('LIMITED-LIABILITY-PARTNERSHIP') ||
          companyName.includes('LIMITED LIABILITY PARTNERSHIP')
        ) {
          /// this api get company details
          let body = {
            LLPID: company_data?.LLPID,
            InstaUserID: '0',
          };
          html1 = await this.api.post(nInstafinaceLlpComapny, body);
          if (html1 === k500Error) return kInternalError;
          finalData = await this.getLLCompanyDataUsingInsta(html1.d);
          if (!finalData || finalData === k500Error) return kInternalError;
          /// this api get company director data of LLP company
          html2 = await this.api.post(nInstafinaceLlpDirectorName, body);
          if (html2 === k500Error) return kInternalError;
          finalData2 = await this.getLLPDirectorNamesUsingInsta(html2.d);
          if (!finalData2 || finalData2 === k500Error) return kInternalError;
        } else {
          /// this api get company details private company
          url = `${nInstafinacePrivateComapny}${companyName}/${cin}`;
          html1 = await this.api.get(url);
          if (html1 === k500Error) return kInternalError;
          finalData = await this.getPrivateCompanyDataUsingInstaNew(html1);
          if (!finalData || finalData == k500Error) return kInternalError;
          /// this api get company director data of private company
          url2 = `${nInstafinacePrivateDirectorName}/${companyName}/${cin}`;
          html2 = await this.api.get(url2);
          if (html2 === k500Error) return kInternalError;
          finalData2 = await this.getDirectorNamesUsingInstaNew(html2);
          if (!finalData2 || finalData2 === k500Error) return kInternalError;
        }
        companyDetails = { ...finalData, ...finalData2 };
        finalJsonData = {
          id,
          companyName,
          cin,
          companyDetails,
          source: 'INSTAFINANCE',
        };
      }

      try {
        if (typeof companyDetails == 'string')
          companyDetails = JSON.parse(companyDetails);
        const companyStatus =
          companyDetails['Company Status'] ??
          companyDetails['LLP Status'] ??
          companyDetails['Company_Status'] ??
          companyDetails['LLP_Status'];

        const companyStartDate =
          companyDetails['Age (Incorp. Date)'] ??
          companyDetails['Date_of_Incorporation'];

        if (!companyStatus || !companyStartDate) {
          const slackData = JSON.stringify({ companyName, userId });
          const threads = [
            `*INSTAFIN RESPONSE FAIL companyStatus or companyStartDate not exist*`,
          ];
          threads.push(slackData);
          this.slackService.sendMsg({
            text: `*INSTAFIN RESPONSE FAIL*`,
            threads,
          });
          return k500Error;
        }
      } catch (error) {
        return k500Error;
      }
    }
    //block the company according to condition and update company
    return await this.companyBlock(finalJsonData, userId);
  }

  async companyBlock(finalJsonData, userId) {
    try {
      const adminId = SYSTEM_ADMIN_ID;
      let companyName = finalJsonData?.companyName ?? '-';
      companyName = companyName.split('-').join(' ');
      let companyDetails = finalJsonData?.companyDetails ?? '-';
      const id = finalJsonData.id;
      /// check company details is json stringify or not
      if (typeof companyDetails == 'string')
        companyDetails = JSON.parse(companyDetails);
      let status =
        companyDetails['Company Status'] ??
        companyDetails['LLP Status'] ??
        companyDetails['Company_Status'] ??
        companyDetails['LLP_Status'];
      status = status?.toLowerCase();
      /// get date
      let date =
        companyDetails['Age (Incorp. Date)']?.split('(')[1] ??
        companyDetails['Date_of_Incorporation'] ??
        '-';
      date = date.replace(')', '');
      /// convert into date format
      if (date)
        date = date.includes('-')
          ? date.split('-').reverse().join('-')
          : date.split('/').reverse().join('-');

      // Fallback for invalid date
      if (date == '-') {
        try {
          let rawDate = companyDetails['Age (Incorp. Date)'] ?? '';
          if (rawDate.endsWith(' Years')) {
            const year = +rawDate.split(' ')[0];
            date = new Date();
            date.setFullYear(date.getFullYear() - year);
            date = date.toJSON().substring(0, 10);
          }
        } catch (error) {
          console.log({ error });
        }
      }
      date = date != '-' && date?.length ? new Date(date) : new Date();
      date = date.toJSON().substring(0, 10);
      const day = this.typeService.dateDifference(new Date(date), new Date());

      //check Incorp. Date and Status if day is less then 365 or status is deactive the block the company
      if (finalJsonData.id) {
        //update company data
        const update: any = {
          companyDetails,
          // CIN: finalJsonData.cin,
          establishedDate: date ?? new Date(),
          forMigration: null,
          // source : 'INSTAFINANCE'
          source: finalJsonData.source,
        };
        if (finalJsonData.cin) update.CIN = finalJsonData.cin;
        const id = finalJsonData.id;
        const data = await this.CompanyRepo.updateRowData(update, id);
        if (data == k500Error) return kInternalError;
      }

      const options = { where: { companyName } };
      const blackListedData = await this.blackListedCompanyRepo.getRowWhereData(
        ['companyName', 'adminId'],
        options,
      );
      if (blackListedData == k500Error) return kInternalError;
      if (blackListedData) return false;
      const eligibleCompanyStatus = [
        'active',
        'amalgamated',
        'converted to llp',
      ];
      if (
        eligibleCompanyStatus.includes(status) &&
        day >= COMPANY_INCORPORATION_MIN_DAY
      )
        return true;
      // cool off Data
      let nextApplyDate = new Date();
      let reasonId;
      let reason;
      if (!eligibleCompanyStatus.includes(status)) {
        await this.adminService.blackListCompanies(
          companyName,
          adminId,
          'SYSTEM',
        );
        nextApplyDate.setDate(nextApplyDate.getDate() + 60);
        reason = COMPANY_STATUS_IS_NOT_ACTIVE;
        reasonId = 55;
      } else if (!(day >= COMPANY_INCORPORATION_MIN_DAY)) {
        const reamingDays = COMPANY_INCORPORATION_MIN_DAY + 1 - day;
        nextApplyDate.setDate(nextApplyDate.getDate() + reamingDays);
        reason = INCORPORATE_DATE_IS_LESS_THAN_ONE_YEAR;
        reasonId = 54;
      }
      //add user in coolOff
      let coolOffData: any = {
        userId,
        type: '2',
        nextApplyDate,
        adminId: SYSTEM_ADMIN_ID,
        status: '0',
        reason,
        reasonId,
      };
      await this.adminService.changeBlacklistUser(coolOffData);
      return false;
    } catch (error) {
      console.error('Error in: ', error);
      return kInternalError;
    }
  }

  async getLLCompanyDataUsingInsta(html) {
    try {
      let startIndex = html[1].indexOf('<tbody>');
      let endIndex = html[1].indexOf('</tbody>');
      let finaltxt = html[1].substring(startIndex, endIndex);
      const list = finaltxt.split('<tr>');
      let final_list = [];
      for (let i = 0; i < list.length; i++) {
        try {
          const ele = list[i];
          const temp = ele.split('<td');
          for (let j = 0; j < temp.length; j++) {
            try {
              let d = temp[j];
              if (d.includes('<tbody>')) d = d.replace('<tbody>', '');
              if (d.includes('>')) {
                d = d.split('>')[1];
                d = d.split('</td')[0];
                d = d.trim();
                final_list.push(d);
              }
            } catch (error) {}
          }
        } catch (error) {}
      }
      let result = {};

      for (let i = 0; i < final_list.length; i += 2) {
        try {
          const key = final_list[i];
          const value = final_list[i + 1];
          if (!key) continue;
          result[key] = value ?? '-';
        } catch (error) {}
      }
      return result;
    } catch (error) {
      return k500Error;
    }
  }

  async getLLPDirectorNamesUsingInsta(html) {
    try {
      let startIndex = html[1].indexOf('<tbody>');
      let endIndex = html[1].indexOf('</tbody>');
      let finaltxt = html[1].substring(startIndex, endIndex);
      const list = finaltxt.split('<tr>');
      const final_list = [];
      for (let i = 0; i < list.length; i++) {
        try {
          let ele = list[i];
          if (ele.includes('<tbody>')) ele = ele.replace('<tbody>', '');
          const temp = ele.split("<td scope='row'>");
          for (let j = 0; j < temp.length; j++) {
            let d = temp[j];
            if (d.includes("<a target='_blank'")) {
              d = d.split('>')[1];
              d = d.split('</a')[0];
              d = d.trim();
              final_list.push(d);
            }
          }
        } catch (error) {}
      }
      const directorObject = {
        'Director Names': final_list,
      };
      return directorObject;
    } catch (error) {
      return k500Error;
    }
  }

  async getPrivateCompanyDataUsingInsta(html) {
    const startIndexTXT = 'companyContentHolder_companyHighlightsContainer';
    const endIndexTXT = 'companyContentHolder_companyIndustryClassification';
    const startIndex = html.indexOf(startIndexTXT);
    const endIndex = html.indexOf(endIndexTXT);
    const finaltxt = html.substring(startIndex, endIndex);
    const list = finaltxt.split('<tr>');
    const final_list = [];
    for (let index = 0; index < list.length; index++) {
      const ele = list[index];
      const temp = ele.split('<td');
      for (let i = 0; i < temp.length; i++) {
        let d = temp[i];
        if (d.includes("scope='row'")) {
          d = d.replace("scope='row'>", '');
          d = d.trim();
          if (d.includes('<a ')) {
            let index = d.indexOf('<a');
            d = d.substring(index);
            index = d.indexOf('>');
            d = d.substring(index + 1);
          }
          d = d.replace('</td>', '');
          d = d.replace('</a>', '');
        } else {
          if (d.includes('<a ')) {
            const index = d.indexOf('<a');
            d = d.substring(index);
          }
          const index = d.indexOf('>');
          d = d.substring(index + 1);
          d = d.replace('</td>', '');
          d = d.replace('</a>', '');
          d = d.replace('</tr>', '');
          if (d.includes('</tbody></table>'))
            d = d.substring(0, d.indexOf('</tbody></table>'));
        }
        d = d.trim();
        if (d && !d.includes('<table ')) final_list.push(d);
      }
    }
    let result = {};

    for (let i = 0; i < final_list.length; i += 2) {
      const key = final_list[i];
      const value = final_list[i + 1];
      result[key] = value;
    }
    return result;
  }

  async getDirectorNamesUsingInsta(html2) {
    try {
      const startIndexTXT = 'directorContentHolder_currentDirectorsContainer';
      const endIndexTXT = 'directorContentHolder_signatoriesContainer';
      const startIndex = html2.indexOf(startIndexTXT);
      const endIndex = html2.indexOf(endIndexTXT);
      const finaltxt = html2.substring(startIndex, endIndex);
      const list = finaltxt.split('<tr>');
      const final_list = [];
      for (let index = 0; index < list.length; index++) {
        const ele = list[index];
        const temp = ele.split('<td');
        for (let i = 0; i < temp.length; i++) {
          let d = temp[i];
          if (d.includes("scope='row'")) {
            d = d.replace("scope='row'>", '');
            d = d.trim();
            if (d.includes('<a ')) {
              let index = d.indexOf('<a');
              d = d.substring(index);
              index = d.indexOf('>');
              d = d.substring(index + 1);
            }
            d = d.replace('</td>', '');
            d = d.replace('</a>', '');
            d = d.trim();
            if (d && !d.includes("scope='row'")) final_list.push(d);
          }
        }
      }
      const directorObject = {
        'Director Names': final_list,
      };
      return directorObject;
    } catch (error) {
      return k500Error;
    }
  }

  async getUanDetails(reqData: any) {
    const mobileNo = reqData?.mobileNo;
    const appType = reqData?.appType;
    const typeOfService = reqData?.typeOfService;
    if (!mobileNo) return kParamMissing('mobileNo');
    if (typeof mobileNo != 'string') return kInvalidParamValue('mobileNo');
    if (!validAppTypeValues.includes(appType))
      return kInvalidParamValue('appType');
    //1 for protean and 2 for befisc
    if (![1, 2].includes(typeOfService))
      return kInvalidParamValue('typeOfService');

    const hashPhone = this.cryptService.getMD5Hash(mobileNo);
    const encryptedPhone = this.cryptService.encryptPhone(mobileNo);

    const options: any = {
      where: { hashPhone: hashPhone, typeOfService },
    };
    const epfoDbData = await this.repoManager.getRowWhereData(
      EPFODetailsEntity,
      null,
      options,
    );
    if (epfoDbData == k500Error) return kInternalError;

    const dayDiff = epfoDbData
      ? this.typeService.differenceInDays(epfoDbData.updatedAt, new Date())
      : 0;

    //if data exist in db and less than MIN_DAYS_FOR_NEXT_EPFO_UPDATE day give old one
    if (dayDiff <= MIN_DAYS_FOR_NEXT_EPFO_UPDATE && epfoDbData)
      return epfoDbData;
    let uanDetails: any;
    //for protean
    if (typeOfService == 1)
      uanDetails = await this.proteanService.uanLookup(reqData);
    //for befisc
    else if (typeOfService == 2)
      uanDetails = await this.befiscService.mobileToUan(reqData);
    if (uanDetails?.message) return uanDetails;
    const uanNumbers = uanDetails?.uan ?? [];

    const userIdData = await this.repoManager.getRowWhereData(
      HashPhoneEntity,
      ['userId'],
      { where: { hashPhone: hashPhone } },
    );
    if (userIdData == k500Error) return kInternalError;

    let finalEpfoData: any = {
      hashPhone,
      phone: encryptedPhone,
      userId: userIdData?.userId ?? null,
      typeOfService,
    };
    if (!uanNumbers?.length) {
      await this.repoManager.createRowData(EPFODetailsEntity, finalEpfoData);
      return finalEpfoData;
    }

    let epfoData: any;
    // if protean UAN service
    if (typeOfService == 1 && uanNumbers?.length)
      // change fun if need to change get employment details api of protean
      epfoData = await this.proteanService.getEPFODataUsingUanValidationS(
        uanNumbers,
        appType,
      );
    //if befisc UAN service
    else if (typeOfService == 2 && uanNumbers?.length)
      epfoData = await this.befiscService.getEPFODataUsingUanToEmployment(
        uanNumbers,
        appType,
      );
    if (epfoData?.message) return epfoData;
    finalEpfoData = {
      ...finalEpfoData,
      ...epfoData,
    };
    if (!epfoDbData?.id)
      await this.repoManager.createRowData(EPFODetailsEntity, finalEpfoData);
    else
      await this.repoManager.updateRowData(
        EPFODetailsEntity,
        finalEpfoData,
        epfoDbData?.id,
      );
    return finalEpfoData;
  }

  extractHighlightCardData(html: string): Record<string, string> {
    const cardRegex =
      /<div[^>]*class="[^"]*\bhighlight-card\b[^"]*"[^>]*>[\s\S]*?<h3[^>]*>(.*?)<\/h3>[\s\S]*?(?:<p[^>]*>(.*?)<\/p>|<div[^>]*>(.*?)<\/div>)/gi;

    const result: Record<string, string> = {};
    let match;
    while ((match = cardRegex.exec(html))) {
      const label = match[1]?.trim().replace(/<[^>]*>/g, '');
      const value = (match[2] || match[3] || '')
        .replace(/<[^>]*>/g, '')
        .replace(/\s+/g, ' ')
        .trim();
      if (label) result[label] = value;
    }
    return result;
  }

  extractValue(html: any, label: string): string | undefined {
    const regex = new RegExp(
      `<td[^>]*>${label.replace(/\./g, '\\.')}<\\/td>\\s*<td[^>]*>(.*?)<\\/td>`,
      'i',
    );
    const match = html.match(regex);
    return match ? match[1].replace(/<[^>]+>/g, '').trim() : undefined;
  }

  extractCIN(html: any, label: string): string | undefined {
    const regex = new RegExp(
      `<tr[^>]*>[^<]*<td[^>]*>\\s*(?:<[^>]+>\\s*)*${label
        .replace(/\./g, '\\.')
        .replace(
          /\s+/g,
          '\\s*',
        )}\\s*(?:<[^>]+>\\s*)*<\\/td>[^<]*<td[^>]*>(.*?)<\\/td>`,
      'is',
    );
    const match = html.match(regex);
    return match ? match[1].replace(/<[^>]+>/g, '').trim() : undefined;
  }

  async getPrivateCompanyDataUsingInstaNew(html) {
    try {
      const dataMap = this.extractHighlightCardData(html);

      // Build your object using expected labels from the cards
      const companyData = {
        'Company Status': dataMap['Company Status'] || '',
        Date_of_Incorporation:
          dataMap['Incorporation Date'] || dataMap['Incorp. Date'] || '',
        balanceSheetDate: dataMap['Balance Sheet Date'],
        industry: dataMap['Industry'] || '',
        companyCIN: this.extractCIN(html, 'Company CIN') || '',
        companyCategory: this.extractValue(html, 'Company Category') || '',
        companyClass: this.extractValue(html, 'Company Class') || '',
        activeCompliant: this.extractValue(html, 'Active Compliant') || '',
        statusUnderCIRP: this.extractValue(html, 'Status Under CIRP') || '',
        filigStatus:
          this.extractValue(html, 'Filing Status For Last 2 Years') || '',
        emailID: this.extractValue(html, 'Email ID') || '',
        website: this.extractValue(html, 'Website') || '',
        registrationNo: this.extractValue(html, 'Registration No') || '',
        companySubCategory:
          this.extractValue(html, 'Company SubCategory') || '',
        address:
          this.extractValue(html, 'Registered Office Address') ||
          this.extractValue(html, 'Address') ||
          '',
        authorisedCapital: this.extractValue(html, 'Authorised Capital') || '',
        paidUpCapital: this.extractValue(html, 'Paid up Capital') || '',
      };

      return companyData;
    } catch (error) {
      const slackData = JSON.stringify({ error });
      const threads = [slackData];
      this.slackService.sendMsg({
        text: `*INSTAFIN getPrivateCompanyDataUsingInstaNew FAIL*`,
        threads,
      });
      return k500Error;
    }
  }

  async getDirectorNamesUsingInstaNew(html) {
    try {
      const regex =
        /<td[^>]*>\s*<a[^>]*href=["'][^"']*\/director\/[^"']*["'][^>]*>(.*?)<\/a>/gi;
      let match;

      const directors = [];
      while ((match = regex.exec(html)) !== null) {
        const text = match[1]
          .replace(/<[^>]+>/g, '') // remove nested tags
          .replace(/\s+/g, ' ') // collapse spaces
          .trim();
        if (text && !/^\d+$/.test(text)) {
          // only keep non-numeric (skip DINs)
          directors.push(text);
        }
      }

      return { directors };
    } catch (error) {
      const slackData = JSON.stringify({ error });
      const threads = [slackData];
      this.slackService.sendMsg({
        text: `*INSTAFIN getDirectorNamesUsingInstaNew FAIL*`,
        threads,
      });
      return k500Error;
    }
  }
}
